/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_0 */
#define PWM_0_INST                                                         TIMG0
#define PWM_0_INST_IRQHandler                                   TIMG0_IRQHandler
#define PWM_0_INST_INT_IRQN                                     (TIMG0_INT_IRQn)
#define PWM_0_INST_CLK_FREQ                                             32000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_0_C0_PORT                                                 GPIOA
#define GPIO_PWM_0_C0_PIN                                         DL_GPIO_PIN_12
#define GPIO_PWM_0_C0_IOMUX                                      (IOMUX_PINCM34)
#define GPIO_PWM_0_C0_IOMUX_FUNC                     IOMUX_PINCM34_PF_TIMG0_CCP0
#define GPIO_PWM_0_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_0_C1_PORT                                                 GPIOA
#define GPIO_PWM_0_C1_PIN                                         DL_GPIO_PIN_13
#define GPIO_PWM_0_C1_IOMUX                                      (IOMUX_PINCM35)
#define GPIO_PWM_0_C1_IOMUX_FUNC                     IOMUX_PINCM35_PF_TIMG0_CCP1
#define GPIO_PWM_0_C1_IDX                                    DL_TIMER_CC_1_INDEX




/* Port definition for Pin Group PA18 */
#define PA18_PORT                                                        (GPIOB)

/* Defines for PA18PIN: GPIOB.17 with pinCMx 43 on package pin 14 */
#define PA18_PA18PIN_PIN                                        (DL_GPIO_PIN_17)
#define PA18_PA18PIN_IOMUX                                       (IOMUX_PINCM43)
/* Port definition for Pin Group PB21 */
#define PB21_PORT                                                        (GPIOB)

/* Defines for PB21PIN: GPIOB.21 with pinCMx 49 on package pin 20 */
#define PB21_PB21PIN_PIN                                        (DL_GPIO_PIN_21)
#define PB21_PB21PIN_IOMUX                                       (IOMUX_PINCM49)
/* Port definition for Pin Group PA0 */
#define PA0_PORT                                                         (GPIOA)

/* Defines for PA0PIN: GPIOA.0 with pinCMx 1 on package pin 33 */
#define PA0_PA0PIN_PIN                                           (DL_GPIO_PIN_0)
#define PA0_PA0PIN_IOMUX                                          (IOMUX_PINCM1)
/* Port definition for Pin Group PB27 */
#define PB27_PORT                                                        (GPIOB)

/* Defines for PB27PIN: GPIOB.27 with pinCMx 58 on package pin 29 */
#define PB27_PB27PIN_PIN                                        (DL_GPIO_PIN_27)
#define PB27_PB27PIN_IOMUX                                       (IOMUX_PINCM58)
/* Port definition for Pin Group LED */
#define LED_PORT                                                         (GPIOB)

/* Defines for PIN_0: GPIOB.9 with pinCMx 26 on package pin 61 */
#define LED_PIN_0_PIN                                            (DL_GPIO_PIN_9)
#define LED_PIN_0_IOMUX                                          (IOMUX_PINCM26)
/* Port definition for Pin Group AIN */
#define AIN_PORT                                                         (GPIOB)

/* Defines for AIN1: GPIOB.6 with pinCMx 23 on package pin 58 */
#define AIN_AIN1_PIN                                             (DL_GPIO_PIN_6)
#define AIN_AIN1_IOMUX                                           (IOMUX_PINCM23)
/* Defines for AIN2: GPIOB.0 with pinCMx 12 on package pin 47 */
#define AIN_AIN2_PIN                                             (DL_GPIO_PIN_0)
#define AIN_AIN2_IOMUX                                           (IOMUX_PINCM12)
/* Defines for L3: GPIOB.3 with pinCMx 16 on package pin 51 */
#define huidu_L3_PORT                                                    (GPIOB)
#define huidu_L3_PIN                                             (DL_GPIO_PIN_3)
#define huidu_L3_IOMUX                                           (IOMUX_PINCM16)
/* Defines for L1: GPIOA.27 with pinCMx 60 on package pin 31 */
#define huidu_L1_PORT                                                    (GPIOA)
#define huidu_L1_PIN                                            (DL_GPIO_PIN_27)
#define huidu_L1_IOMUX                                           (IOMUX_PINCM60)
/* Defines for R1: GPIOB.24 with pinCMx 52 on package pin 23 */
#define huidu_R1_PORT                                                    (GPIOB)
#define huidu_R1_PIN                                            (DL_GPIO_PIN_24)
#define huidu_R1_IOMUX                                           (IOMUX_PINCM52)
/* Defines for R3: GPIOA.8 with pinCMx 19 on package pin 54 */
#define huidu_R3_PORT                                                    (GPIOA)
#define huidu_R3_PIN                                             (DL_GPIO_PIN_8)
#define huidu_R3_IOMUX                                           (IOMUX_PINCM19)
/* Port definition for Pin Group BIN */
#define BIN_PORT                                                         (GPIOB)

/* Defines for BIN1: GPIOB.8 with pinCMx 25 on package pin 60 */
#define BIN_BIN1_PIN                                             (DL_GPIO_PIN_8)
#define BIN_BIN1_IOMUX                                           (IOMUX_PINCM25)
/* Defines for BIN2: GPIOB.7 with pinCMx 24 on package pin 59 */
#define BIN_BIN2_PIN                                             (DL_GPIO_PIN_7)
#define BIN_BIN2_IOMUX                                           (IOMUX_PINCM24)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_0_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
