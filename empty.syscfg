/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const GPIO5  = GPIO.addInstance();
const GPIO6  = GPIO.addInstance();
const GPIO7  = GPIO.addInstance();
const GPIO8  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                         = "AIN";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].$name       = "AIN1";
GPIO1.associatedPins[0].pin.$assign = "PB6";
GPIO1.associatedPins[1].$name       = "AIN2";
GPIO1.associatedPins[1].pin.$assign = "PB0";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                         = "huidu";
GPIO2.associatedPins.create(4);
GPIO2.associatedPins[0].direction   = "INPUT";
GPIO2.associatedPins[0].$name       = "L3";
GPIO2.associatedPins[0].pin.$assign = "PB3";
GPIO2.associatedPins[1].direction   = "INPUT";
GPIO2.associatedPins[1].$name       = "L1";
GPIO2.associatedPins[1].pin.$assign = "PA27";
GPIO2.associatedPins[2].$name       = "R1";
GPIO2.associatedPins[2].direction   = "INPUT";
GPIO2.associatedPins[2].pin.$assign = "PB24";
GPIO2.associatedPins[3].direction   = "INPUT";
GPIO2.associatedPins[3].$name       = "R3";
GPIO2.associatedPins[3].pin.$assign = "PA8";

GPIO3.$name                         = "BIN";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name       = "BIN1";
GPIO3.associatedPins[0].pin.$assign = "PB8";
GPIO3.associatedPins[1].$name       = "BIN2";
GPIO3.associatedPins[1].pin.$assign = "PB7";

GPIO4.$name                              = "PA18";
GPIO4.associatedPins[0].$name            = "PA18PIN";
GPIO4.associatedPins[0].direction        = "INPUT";
GPIO4.associatedPins[0].internalResistor = "PULL_UP";
GPIO4.associatedPins[0].assignedPin      = "17";
GPIO4.associatedPins[0].assignedPort     = "PORTB";
GPIO4.associatedPins[0].pin.$assign      = "PB17";

GPIO5.$name                              = "PB21";
GPIO5.associatedPins[0].$name            = "PB21PIN";
GPIO5.associatedPins[0].direction        = "INPUT";
GPIO5.associatedPins[0].assignedPort     = "PORTB";
GPIO5.associatedPins[0].assignedPin      = "21";
GPIO5.associatedPins[0].internalResistor = "PULL_UP";
GPIO5.associatedPins[0].pin.$assign      = "PB21";

GPIO6.$name                          = "PA0";
GPIO6.associatedPins[0].$name        = "PA0PIN";
GPIO6.associatedPins[0].assignedPort = "PORTA";
GPIO6.associatedPins[0].assignedPin  = "0";

GPIO7.$name                          = "PB27";
GPIO7.associatedPins[0].$name        = "PB27PIN";
GPIO7.associatedPins[0].assignedPort = "PORTB";
GPIO7.associatedPins[0].assignedPin  = "27";

GPIO8.$name                         = "LED";
GPIO8.associatedPins[0].$name       = "PIN_0";
GPIO8.associatedPins[0].pin.$assign = "PB9";

PWM1.$name                      = "PWM_0";
PWM1.dutyArgs                   = "[25,75,0,0]";
PWM1.timerCount                 = 3200;
PWM1.timerStartTimer            = true;
PWM1.peripheral.ccp0Pin.$assign = "PA12";
PWM1.peripheral.ccp1Pin.$assign = "PA13";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle    = 50;
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.dutyCycle    = 50;
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

SYSCTL.forceDefaultClkConfig = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO6.associatedPins[0].pin.$suggestSolution = "PA0";
GPIO7.associatedPins[0].pin.$suggestSolution = "PB27";
PWM1.peripheral.$suggestSolution             = "TIMG0";
SYSCTL.peripheral.$suggestSolution           = "SYSCTL";
