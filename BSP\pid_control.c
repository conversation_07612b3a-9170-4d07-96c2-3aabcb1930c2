#include "pid_control.h"

//定义PID参数
#define Kp 2
#define Ki 0.03
#define Kd 0.028


//定义PID控制量
float last_error = 0;
float integral = 0;
float derivative = 0;
float pid_output = 0;


//计算当前误差函数
float calculateError(uint32_t L3, uint32_t L1, uint32_t R1, uint32_t R3) 
{
    // 基于传感器位置的加权误差
    float error = 0;
    if (L3) error -= 3.0;      // 最左侧，权重最大
    if (L1) error -= 1.0;      // 左侧
    if (R1) error += 1.0;      // 右侧  
    if (R3) error += 3.0;      // 最右侧，权重最大
    
    return error;
}

//pid计算函数
float pid_Calc(float error)//参数为实际误差
{
    integral += error;//积分项计算

    derivative = error - last_error;//微分项计算

     // 积分限幅，防止积分饱和
    if (integral > 50) integral = 50;
    if (integral < -50) integral = -50;

    pid_output = Kp * error + Ki * integral + Kd * derivative;
    last_error = error;//计算pid的结果

    // 限制PID输出范围
    if (pid_output > max_adjustment) pid_output = max_adjustment;
    if (pid_output < -max_adjustment) pid_output = -max_adjustment;
    
    return pid_output;
}

//设置左转函数
void TurnLeft()
{
    pid_TurnLeft();

}

//设置右转函数
void TurnRight()
{
    pid_TurnRight();
    
}

void TurnStraight()
{
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);

     //重置积分项
     integral = 0;
}

void TurnMiniLeft()
{
    //计算微调参数
    float error = calculateError(L3,L1,R1,R3);
    pid_output = pid_Calc(error);

    //映射PWM
    uint8_t left_duty = base_duty - (uint8_t)fabs(pid_output);
    uint8_t right_duty = base_duty + (uint8_t)fabs(pid_output);

    //限幅
    if (left_duty < 5) left_duty = 5;
    if (right_duty > 100) right_duty = 100;

    //进行微调
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(left_duty);
    setPWMRight(right_duty);
}

void TurnMiniright()
{
    //计算微调参数
    float error = calculateError(L3,L1,R1,R3);
    pid_output = pid_Calc(error);


    //映射PWM
    uint8_t left_duty = base_duty + (uint8_t)fabs(pid_output);
    uint8_t right_duty = base_duty - (uint8_t)fabs(pid_output);

    //限幅
    if (left_duty < 5) left_duty = 5;
    if (right_duty > 100) right_duty = 100;

    //进行微调
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(left_duty);
    setPWMRight(right_duty);
}




