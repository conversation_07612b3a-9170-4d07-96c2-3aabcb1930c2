#ifndef __PID_TURN_H
#define __PID_TURN_H

#include "ti_msp_dl_config.h"
#include "ti/driverlib/dl_timer.h"
#include"pwm.h"

extern uint32_t L3;
extern uint32_t L1;
extern uint32_t R1;
extern uint32_t R3;
extern volatile uint8_t count;        // 添加count变量声明
extern volatile uint8_t STOP_COUNT;   // 添加STOP_COUNT变量声明

//占空比初始量
extern uint8_t base_duty;      // 基础占空比
extern uint8_t turn_duty;      // 转弯占空比
extern uint8_t max_adjustment; // PID最大调整量


void pid_TurnLeft();
void pid_TurnRight();
void pid_TurnRight_New();



#endif