/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"
#include "ti/driverlib/dl_timer.h"
#include "BSP/pid_control.h"

volatile uint8_t turning_flag = 0;  //设置转弯标志位

//占空比初始量
uint8_t base_duty = 20;      // 基础占空比
uint8_t turn_duty = 25;      // 转弯占空比
uint8_t max_adjustment = 15; // PID最大调整量


// 声明外部变量
uint32_t L3;
uint32_t L1;
uint32_t R1;
uint32_t R3;

//启动变量
volatile uint8_t count = 0;        // 定义count变量
volatile uint8_t STOP_COUNT = 0;   // 定义STOP_COUNT变量


int main(void)
{
    SYSCFG_DL_init();

    while (1) 
    {
        if(!DL_GPIO_readPins(PA18_PORT,PA18_PA18PIN_PIN))
        {
            STOP_COUNT++;
            DL_GPIO_setPins(PA0_PORT,PA0_PA0PIN_PIN);
            delay_ms(500);
        }
        else if (DL_GPIO_readPins(PA18_PORT,PA18_PA18PIN_PIN))
        {
            DL_GPIO_clearPins(PA0_PORT,PA0_PA0PIN_PIN);
        }

        if(!DL_GPIO_readPins(PB21_PORT,PB21_PB21PIN_PIN))
        {
            DL_GPIO_setPins(PB27_PORT,PB27_PB27PIN_PIN);
            delay_ms(500);
            break;
        }
    }

    while (1) 
    {
        if (count>=STOP_COUNT*4) 
        {
            leftMotorStop();
            rightMotorStop();
           continue; // 跳过后续处理
        }

            // LED调试 - 显示转弯状态

        L3 =  DL_GPIO_readPins(huidu_L3_PORT, huidu_L3_PIN); 
        L1 =  DL_GPIO_readPins(huidu_L1_PORT, huidu_L1_PIN); 
        R1 =  DL_GPIO_readPins(huidu_R1_PORT, huidu_R1_PIN); 
        R3 =  DL_GPIO_readPins(huidu_R3_PORT, huidu_R3_PIN); 

        if (L3 && !turning_flag ) 
        {
            turning_flag = 1;//设置标志位
            DL_GPIO_setPins(LED_PORT, LED_PIN_0_PIN);  // 转弯时点亮LED
            TurnLeft();
            DL_GPIO_clearPins(LED_PORT, LED_PIN_0_PIN); // 正常时熄灭LED
            turning_flag = 0;//清除标志位
        }
        else if (R3 && !turning_flag)
        {
            turning_flag = 1;//设置标志位
            DL_GPIO_setPins(LED_PORT, LED_PIN_0_PIN);  // 转弯时点亮LED
            TurnRight();
            DL_GPIO_clearPins(LED_PORT, LED_PIN_0_PIN); // 正常时熄灭LED
            turning_flag = 0;//清除标志位
        }
        else if (L1 && R1)
        {
            TurnStraight();
        }
        else if (L1)
        {
            TurnMiniLeft();
        }
        else if (R1)
        {
            TurnMiniright();
        }
        else 
        {
            TurnStraight();
        }
    }
}
