#ifndef __PID_CONTROL_H
#define __PID_CONTROL_H


#include "ti_msp_dl_config.h"
#include "ti/driverlib/dl_timer.h"
#include"pwm.h"
#include"pid_Turn.h"


extern uint32_t L3;
extern uint32_t L1;
extern uint32_t R1;
extern uint32_t R3;
extern volatile uint8_t count;        // 添加count变量声明
extern volatile uint8_t STOP_COUNT;   // 添加STOP_COUNT变量声明


float calculateError(uint32_t L3, uint32_t L1, uint32_t R1, uint32_t R3) ;
float pid_Calc(float error);
void TurnLeft();
void TurnRight();
void TurnStraight();
void TurnMiniLeft();
void TurnMiniright();
#endif
