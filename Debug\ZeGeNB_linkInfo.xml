<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o ZeGeNB.out -mZeGeNB.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/ZeGeNB -iC:/Users/<USER>/workspace_ccstheia/ZeGeNB/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=ZeGeNB_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./BSP/pid_Turn.o ./BSP/pid_control.o ./BSP/pwm.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x688de6e8</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\ZeGeNB.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xf89</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>pid_Turn.o</file>
         <name>pid_Turn.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>pid_control.o</file>
         <name>pid_control.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>pwm.o</file>
         <name>pwm.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\ZeGeNB\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-2c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-e3">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e4">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-e5">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-e6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-e7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-118">
         <name>.text.adddf3_subdf3</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x192</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x252</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x252</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0x254</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x254</run_address>
         <size>0x128</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.pid_Calc</name>
         <load_address>0x37c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x37c</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x488</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x488</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.__muldf3</name>
         <load_address>0x58c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x58c</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text</name>
         <load_address>0x670</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x670</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text.TurnMiniLeft</name>
         <load_address>0x748</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x748</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-91">
         <name>.text.TurnMiniright</name>
         <load_address>0x7f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7f8</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-76">
         <name>.text:memcpy</name>
         <load_address>0x8a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8a8</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e6"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x942</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x942</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.SYSCFG_DL_PWM_0_init</name>
         <load_address>0x944</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x944</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x9d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9d4</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.__truncdfsf2</name>
         <load_address>0xa50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa50</run_address>
         <size>0x74</size>
         <alignment>0x10</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.pid_TurnLeft</name>
         <load_address>0xac4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xac4</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.pid_TurnRight</name>
         <load_address>0xb38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb38</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0xbac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbac</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.delay_ms</name>
         <load_address>0xc14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc14</run_address>
         <size>0x68</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0xc7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc7c</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xce0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xce0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0xd20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd20</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.__extendsfdf2</name>
         <load_address>0xd60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd60</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.__floatsisf</name>
         <load_address>0xda0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xda0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.__gtsf2</name>
         <load_address>0xddc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xddc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xe18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe18</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.__eqsf2</name>
         <load_address>0xe54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe54</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.__muldsi3</name>
         <load_address>0xe90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe90</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.__fixsfsi</name>
         <load_address>0xecc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xecc</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xf04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf04</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.TurnStraight</name>
         <load_address>0xf38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf38</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.__floatunsisf</name>
         <load_address>0xf60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf60</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xf88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf88</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0xfb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfb0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xfcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfcc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.setPWMLeft</name>
         <load_address>0xfe8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfe8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.setPWMRight</name>
         <load_address>0x1004</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1004</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1020</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1020</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x1038</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1038</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x104e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x104e</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-130">
         <name>.text.leftMotorBackward</name>
         <load_address>0x1064</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1064</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.leftMotorForward</name>
         <load_address>0x1078</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1078</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text.leftMotorStop</name>
         <load_address>0x108c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x108c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x10a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10a0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x10b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10b4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.rightMotorBackward</name>
         <load_address>0x10c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10c4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.rightMotorForward</name>
         <load_address>0x10d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10d4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-87">
         <name>.text.rightMotorStop</name>
         <load_address>0x10e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10e4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x10f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10f4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.TurnLeft</name>
         <load_address>0x10fe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10fe</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text.TurnRight</name>
         <load_address>0x1106</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1106</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x1110</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1110</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text:abort</name>
         <load_address>0x1118</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1118</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.HOSTexit</name>
         <load_address>0x111e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x111e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1122</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1122</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x1126</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1126</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-195">
         <name>.cinit..data.load</name>
         <load_address>0x1140</load_address>
         <readonly>true</readonly>
         <run_address>0x1140</run_address>
         <size>0xc</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-193">
         <name>__TI_handler_table</name>
         <load_address>0x114c</load_address>
         <readonly>true</readonly>
         <run_address>0x114c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-196">
         <name>.cinit..bss.load</name>
         <load_address>0x1158</load_address>
         <readonly>true</readonly>
         <run_address>0x1158</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-194">
         <name>__TI_cinit_table</name>
         <load_address>0x1160</load_address>
         <readonly>true</readonly>
         <run_address>0x1160</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-101">
         <name>.rodata.gPWM_0Config</name>
         <load_address>0x1130</load_address>
         <readonly>true</readonly>
         <run_address>0x1130</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-100">
         <name>.rodata.gPWM_0ClockConfig</name>
         <load_address>0x1138</load_address>
         <readonly>true</readonly>
         <run_address>0x1138</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-98">
         <name>.data.turning_flag</name>
         <load_address>0x20200015</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200015</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.data.base_duty</name>
         <load_address>0x20200011</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200011</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.data.turn_duty</name>
         <load_address>0x20200014</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200014</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.data.max_adjustment</name>
         <load_address>0x20200013</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200013</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-93">
         <name>.data.count</name>
         <load_address>0x20200012</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200012</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-92">
         <name>.data.STOP_COUNT</name>
         <load_address>0x20200010</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200010</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-128">
         <name>.data.last_error</name>
         <load_address>0x20200008</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200008</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.data.integral</name>
         <load_address>0x20200004</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200004</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-129">
         <name>.data.derivative</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-de">
         <name>.data.pid_output</name>
         <load_address>0x2020000c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020000c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-94">
         <name>.common:L3</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020001c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-95">
         <name>.common:L1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200018</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-96">
         <name>.common:R1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200020</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-97">
         <name>.common:R3</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200024</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-198">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_loc</name>
         <load_address>0x2f</load_address>
         <run_address>0x2f</run_address>
         <size>0x82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_loc</name>
         <load_address>0xb1</load_address>
         <run_address>0xb1</run_address>
         <size>0x2ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_loc</name>
         <load_address>0x35e</load_address>
         <run_address>0x35e</run_address>
         <size>0x76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_loc</name>
         <load_address>0x3d4</load_address>
         <run_address>0x3d4</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_loc</name>
         <load_address>0x3e7</load_address>
         <run_address>0x3e7</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x1e0e</load_address>
         <run_address>0x1e0e</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_loc</name>
         <load_address>0x1ee6</load_address>
         <run_address>0x1ee6</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x230a</load_address>
         <run_address>0x230a</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_loc</name>
         <load_address>0x2476</load_address>
         <run_address>0x2476</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_loc</name>
         <load_address>0x24e5</load_address>
         <run_address>0x24e5</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_loc</name>
         <load_address>0x264c</load_address>
         <run_address>0x264c</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-140">
         <name>.debug_loc</name>
         <load_address>0x2672</load_address>
         <run_address>0x2672</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x12f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_abbrev</name>
         <load_address>0x12f</load_address>
         <run_address>0x12f</run_address>
         <size>0x1b1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x2e0</load_address>
         <run_address>0x2e0</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_abbrev</name>
         <load_address>0x34d</load_address>
         <run_address>0x34d</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_abbrev</name>
         <load_address>0x385</load_address>
         <run_address>0x385</run_address>
         <size>0xf7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_abbrev</name>
         <load_address>0x47c</load_address>
         <run_address>0x47c</run_address>
         <size>0x125</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_abbrev</name>
         <load_address>0x5a1</load_address>
         <run_address>0x5a1</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_abbrev</name>
         <load_address>0x603</load_address>
         <run_address>0x603</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x889</load_address>
         <run_address>0x889</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_abbrev</name>
         <load_address>0x938</load_address>
         <run_address>0x938</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_abbrev</name>
         <load_address>0xaa8</load_address>
         <run_address>0xaa8</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0xae1</load_address>
         <run_address>0xae1</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_abbrev</name>
         <load_address>0xba3</load_address>
         <run_address>0xba3</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0xc13</load_address>
         <run_address>0xc13</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_abbrev</name>
         <load_address>0xca0</load_address>
         <run_address>0xca0</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_abbrev</name>
         <load_address>0xd38</load_address>
         <run_address>0xd38</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_abbrev</name>
         <load_address>0xd64</load_address>
         <run_address>0xd64</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.debug_abbrev</name>
         <load_address>0xd8b</load_address>
         <run_address>0xd8b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.debug_abbrev</name>
         <load_address>0xdb2</load_address>
         <run_address>0xdb2</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-153">
         <name>.debug_abbrev</name>
         <load_address>0xdd9</load_address>
         <run_address>0xdd9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-148">
         <name>.debug_abbrev</name>
         <load_address>0xe00</load_address>
         <run_address>0xe00</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_abbrev</name>
         <load_address>0xe27</load_address>
         <run_address>0xe27</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-151">
         <name>.debug_abbrev</name>
         <load_address>0xe4e</load_address>
         <run_address>0xe4e</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_abbrev</name>
         <load_address>0xe75</load_address>
         <run_address>0xe75</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_abbrev</name>
         <load_address>0xe9c</load_address>
         <run_address>0xe9c</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_abbrev</name>
         <load_address>0xec3</load_address>
         <run_address>0xec3</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_abbrev</name>
         <load_address>0xeea</load_address>
         <run_address>0xeea</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_abbrev</name>
         <load_address>0xf11</load_address>
         <run_address>0xf11</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-152">
         <name>.debug_abbrev</name>
         <load_address>0xf38</load_address>
         <run_address>0xf38</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_abbrev</name>
         <load_address>0xf5d</load_address>
         <run_address>0xf5d</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_abbrev</name>
         <load_address>0xfb6</load_address>
         <run_address>0xfb6</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e6"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_abbrev</name>
         <load_address>0xfdb</load_address>
         <run_address>0xfdb</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa2e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_info</name>
         <load_address>0xa2e</load_address>
         <run_address>0xa2e</run_address>
         <size>0x1f52</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2980</load_address>
         <run_address>0x2980</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_info</name>
         <load_address>0x2a00</load_address>
         <run_address>0x2a00</run_address>
         <size>0x17b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_info</name>
         <load_address>0x2b7b</load_address>
         <run_address>0x2b7b</run_address>
         <size>0x326</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_info</name>
         <load_address>0x2ea1</load_address>
         <run_address>0x2ea1</run_address>
         <size>0xff6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_info</name>
         <load_address>0x3e97</load_address>
         <run_address>0x3e97</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_info</name>
         <load_address>0x3f0c</load_address>
         <run_address>0x3f0c</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x707e</load_address>
         <run_address>0x707e</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_info</name>
         <load_address>0x74a1</load_address>
         <run_address>0x74a1</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_info</name>
         <load_address>0x7be5</load_address>
         <run_address>0x7be5</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x7c2b</load_address>
         <run_address>0x7c2b</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x7dbd</load_address>
         <run_address>0x7dbd</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x7e83</load_address>
         <run_address>0x7e83</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_info</name>
         <load_address>0x7fff</load_address>
         <run_address>0x7fff</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_info</name>
         <load_address>0x80f7</load_address>
         <run_address>0x80f7</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_info</name>
         <load_address>0x8132</load_address>
         <run_address>0x8132</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_info</name>
         <load_address>0x82d9</load_address>
         <run_address>0x82d9</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_info</name>
         <load_address>0x8480</load_address>
         <run_address>0x8480</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.debug_info</name>
         <load_address>0x860d</load_address>
         <run_address>0x860d</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_info</name>
         <load_address>0x879c</load_address>
         <run_address>0x879c</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_info</name>
         <load_address>0x8933</load_address>
         <run_address>0x8933</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_info</name>
         <load_address>0x8ac2</load_address>
         <run_address>0x8ac2</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_info</name>
         <load_address>0x8c55</load_address>
         <run_address>0x8c55</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_info</name>
         <load_address>0x8dec</load_address>
         <run_address>0x8dec</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_info</name>
         <load_address>0x8f81</load_address>
         <run_address>0x8f81</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_info</name>
         <load_address>0x9198</load_address>
         <run_address>0x9198</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_info</name>
         <load_address>0x9331</load_address>
         <run_address>0x9331</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_info</name>
         <load_address>0x94ed</load_address>
         <run_address>0x94ed</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_info</name>
         <load_address>0x96ae</load_address>
         <run_address>0x96ae</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_info</name>
         <load_address>0x9733</load_address>
         <run_address>0x9733</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e6"/>
      </object_component>
      <object_component id="oc-199">
         <name>.debug_info</name>
         <load_address>0x9a2d</load_address>
         <run_address>0x9a2d</run_address>
         <size>0xa2</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x548</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_str</name>
         <load_address>0x548</load_address>
         <run_address>0x548</run_address>
         <size>0x1921</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x1e69</load_address>
         <run_address>0x1e69</run_address>
         <size>0x159</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_str</name>
         <load_address>0x1fc2</load_address>
         <run_address>0x1fc2</run_address>
         <size>0x13c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_str</name>
         <load_address>0x20fe</load_address>
         <run_address>0x20fe</run_address>
         <size>0x1fc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_str</name>
         <load_address>0x22fa</load_address>
         <run_address>0x22fa</run_address>
         <size>0x764</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_str</name>
         <load_address>0x2a5e</load_address>
         <run_address>0x2a5e</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_str</name>
         <load_address>0x2bd6</load_address>
         <run_address>0x2bd6</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x49ad</load_address>
         <run_address>0x49ad</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_str</name>
         <load_address>0x4bd2</load_address>
         <run_address>0x4bd2</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_str</name>
         <load_address>0x4f01</load_address>
         <run_address>0x4f01</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0x4ff6</load_address>
         <run_address>0x4ff6</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_str</name>
         <load_address>0x5191</load_address>
         <run_address>0x5191</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x52f9</load_address>
         <run_address>0x52f9</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_str</name>
         <load_address>0x54ce</load_address>
         <run_address>0x54ce</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_str</name>
         <load_address>0x5616</load_address>
         <run_address>0x5616</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_str</name>
         <load_address>0x56ff</load_address>
         <run_address>0x56ff</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_frame</name>
         <load_address>0x24</load_address>
         <run_address>0x24</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_frame</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_frame</name>
         <load_address>0x128</load_address>
         <run_address>0x128</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_frame</name>
         <load_address>0x200</load_address>
         <run_address>0x200</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_frame</name>
         <load_address>0x2c0</load_address>
         <run_address>0x2c0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_frame</name>
         <load_address>0x2e0</load_address>
         <run_address>0x2e0</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x6e8</load_address>
         <run_address>0x6e8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_frame</name>
         <load_address>0x778</load_address>
         <run_address>0x778</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_frame</name>
         <load_address>0x878</load_address>
         <run_address>0x878</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_frame</name>
         <load_address>0x898</load_address>
         <run_address>0x898</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x8d0</load_address>
         <run_address>0x8d0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x8f8</load_address>
         <run_address>0x8f8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_frame</name>
         <load_address>0x928</load_address>
         <run_address>0x928</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_frame</name>
         <load_address>0x958</load_address>
         <run_address>0x958</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_frame</name>
         <load_address>0x978</load_address>
         <run_address>0x978</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x317</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_line</name>
         <load_address>0x317</load_address>
         <run_address>0x317</run_address>
         <size>0x477</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x78e</load_address>
         <run_address>0x78e</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_line</name>
         <load_address>0x846</load_address>
         <run_address>0x846</run_address>
         <size>0xf3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_line</name>
         <load_address>0x939</load_address>
         <run_address>0x939</run_address>
         <size>0x326</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0xc5f</load_address>
         <run_address>0xc5f</run_address>
         <size>0x370</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_line</name>
         <load_address>0xfcf</load_address>
         <run_address>0xfcf</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_line</name>
         <load_address>0x1148</load_address>
         <run_address>0x1148</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x28b7</load_address>
         <run_address>0x28b7</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_line</name>
         <load_address>0x2a93</load_address>
         <run_address>0x2a93</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_line</name>
         <load_address>0x2fad</load_address>
         <run_address>0x2fad</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_line</name>
         <load_address>0x2feb</load_address>
         <run_address>0x2feb</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x30e9</load_address>
         <run_address>0x30e9</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x31a9</load_address>
         <run_address>0x31a9</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_line</name>
         <load_address>0x3371</load_address>
         <run_address>0x3371</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_line</name>
         <load_address>0x33d8</load_address>
         <run_address>0x33d8</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_line</name>
         <load_address>0x3419</load_address>
         <run_address>0x3419</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_line</name>
         <load_address>0x3520</load_address>
         <run_address>0x3520</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_line</name>
         <load_address>0x3685</load_address>
         <run_address>0x3685</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_line</name>
         <load_address>0x3791</load_address>
         <run_address>0x3791</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_line</name>
         <load_address>0x384a</load_address>
         <run_address>0x384a</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_line</name>
         <load_address>0x390a</load_address>
         <run_address>0x390a</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_line</name>
         <load_address>0x39c2</load_address>
         <run_address>0x39c2</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_line</name>
         <load_address>0x3a7e</load_address>
         <run_address>0x3a7e</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_line</name>
         <load_address>0x3b32</load_address>
         <run_address>0x3b32</run_address>
         <size>0xd1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_line</name>
         <load_address>0x3c03</load_address>
         <run_address>0x3c03</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_line</name>
         <load_address>0x3cca</load_address>
         <run_address>0x3cca</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_line</name>
         <load_address>0x3d6e</load_address>
         <run_address>0x3d6e</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_line</name>
         <load_address>0x3e30</load_address>
         <run_address>0x3e30</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_line</name>
         <load_address>0x3f34</load_address>
         <run_address>0x3f34</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_line</name>
         <load_address>0x3fe9</load_address>
         <run_address>0x3fe9</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e6"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_ranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_ranges</name>
         <load_address>0xd0</load_address>
         <run_address>0xd0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_ranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x2f8</load_address>
         <run_address>0x2f8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_ranges</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_ranges</name>
         <load_address>0x388</load_address>
         <run_address>0x388</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_ranges</name>
         <load_address>0x3f0</load_address>
         <run_address>0x3f0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_ranges</name>
         <load_address>0x408</load_address>
         <run_address>0x408</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_ranges</name>
         <load_address>0x440</load_address>
         <run_address>0x440</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_ranges</name>
         <load_address>0x458</load_address>
         <run_address>0x458</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e6"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-122">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_aranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_aranges</name>
         <load_address>0x180</load_address>
         <run_address>0x180</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_aranges</name>
         <load_address>0x1b0</load_address>
         <run_address>0x1b0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e6"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x1070</size>
         <contents>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1140</load_address>
         <run_address>0x1140</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-194"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1130</load_address>
         <run_address>0x1130</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-100"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-15d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x16</size>
         <contents>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-de"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200018</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-97"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-198"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-154" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-155" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-156" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-157" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-158" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-159" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15b" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-177" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2692</size>
         <contents>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-140"/>
         </contents>
      </logical_group>
      <logical_group id="lg-179" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfea</size>
         <contents>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-19a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17b" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9acf</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-199"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17d" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5892</size>
         <contents>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-13f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17f" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9a8</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-104"/>
         </contents>
      </logical_group>
      <logical_group id="lg-181" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4089</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-75"/>
         </contents>
      </logical_group>
      <logical_group id="lg-183" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x480</size>
         <contents>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-74"/>
         </contents>
      </logical_group>
      <logical_group id="lg-18d" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d8</size>
         <contents>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-72"/>
         </contents>
      </logical_group>
      <logical_group id="lg-197" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-1a3" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1170</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1a4" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1a5" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1170</used_space>
         <unused_space>0x1ee90</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x1070</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1130</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1140</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1170</start_address>
               <size>0x1ee90</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x226</used_space>
         <unused_space>0x7dda</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-159"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-15b"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x16</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200016</start_address>
               <size>0x2</size>
            </available_space>
            <allocated_space>
               <start_address>0x20200018</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200028</start_address>
               <size>0x7dd8</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1140</load_address>
            <load_size>0xc</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x16</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x1158</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200018</run_address>
            <run_size>0x10</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1160</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1170</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1170</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x114c</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x1158</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3c">
         <name>main</name>
         <value>0x255</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-3d">
         <name>STOP_COUNT</name>
         <value>0x20200010</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-3e">
         <name>count</name>
         <value>0x20200012</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-3f">
         <name>L3</name>
         <value>0x2020001c</value>
      </symbol>
      <symbol id="sm-40">
         <name>L1</name>
         <value>0x20200018</value>
      </symbol>
      <symbol id="sm-41">
         <name>R1</name>
         <value>0x20200020</value>
      </symbol>
      <symbol id="sm-42">
         <name>R3</name>
         <value>0x20200024</value>
      </symbol>
      <symbol id="sm-43">
         <name>turning_flag</name>
         <value>0x20200015</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-44">
         <name>base_duty</name>
         <value>0x20200011</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-45">
         <name>turn_duty</name>
         <value>0x20200014</value>
         <object_component_ref idref="oc-12d"/>
      </symbol>
      <symbol id="sm-46">
         <name>max_adjustment</name>
         <value>0x20200013</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-60">
         <name>SYSCFG_DL_init</name>
         <value>0x104f</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-61">
         <name>SYSCFG_DL_initPower</name>
         <value>0xf05</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-62">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0xbad</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-63">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xce1</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-64">
         <name>SYSCFG_DL_PWM_0_init</name>
         <value>0x945</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-6f">
         <name>Default_Handler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>Reset_Handler</name>
         <value>0x1123</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-71">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-72">
         <name>NMI_Handler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>HardFault_Handler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>SVC_Handler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>PendSV_Handler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>SysTick_Handler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>GROUP0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>GROUP1_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>TIMG8_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>UART3_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>ADC0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>ADC1_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>CANFD0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>DAC0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>SPI0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>SPI1_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>UART1_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>UART2_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>UART0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>TIMG0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>TIMG6_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>TIMA0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>TIMA1_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>TIMG7_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>TIMG12_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>I2C0_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>I2C1_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>AES_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>RTC_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>DMA_IRQHandler</name>
         <value>0x253</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>pid_TurnLeft</name>
         <value>0xac5</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-9c">
         <name>pid_TurnRight</name>
         <value>0xb39</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-b4">
         <name>pid_Calc</name>
         <value>0x37d</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-b5">
         <name>integral</name>
         <value>0x20200004</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-b6">
         <name>last_error</name>
         <value>0x20200008</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-b7">
         <name>derivative</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-b8">
         <name>pid_output</name>
         <value>0x2020000c</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-b9">
         <name>TurnLeft</name>
         <value>0x10ff</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-ba">
         <name>TurnRight</name>
         <value>0x1107</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-bb">
         <name>TurnStraight</name>
         <value>0xf39</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-bc">
         <name>TurnMiniLeft</name>
         <value>0x749</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-bd">
         <name>TurnMiniright</name>
         <value>0x7f9</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-df">
         <name>leftMotorForward</name>
         <value>0x1079</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-e0">
         <name>leftMotorBackward</name>
         <value>0x1065</value>
         <object_component_ref idref="oc-130"/>
      </symbol>
      <symbol id="sm-e1">
         <name>leftMotorStop</name>
         <value>0x108d</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-e2">
         <name>rightMotorForward</name>
         <value>0x10d5</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-e3">
         <name>rightMotorBackward</name>
         <value>0x10c5</value>
         <object_component_ref idref="oc-12c"/>
      </symbol>
      <symbol id="sm-e4">
         <name>rightMotorStop</name>
         <value>0x10e5</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-e5">
         <name>setPWMLeft</name>
         <value>0xfe9</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-e6">
         <name>setPWMRight</name>
         <value>0x1005</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-e7">
         <name>delay_ms</name>
         <value>0xc15</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-e8">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e9">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ea">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-eb">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ec">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ed">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ee">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ef">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f0">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f9">
         <name>DL_Common_delayCycles</name>
         <value>0x10f5</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-110">
         <name>DL_Timer_setClockConfig</name>
         <value>0xfcd</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-111">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x10b5</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-112">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0xfb1</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-113">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1021</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-114">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x489</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-11f">
         <name>_c_int00_noargs</name>
         <value>0xf89</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-120">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-12c">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xe19</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-134">
         <name>_system_pre_init</name>
         <value>0x1127</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-13f">
         <name>__TI_zero_init_nomemset</name>
         <value>0x1039</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-148">
         <name>__TI_decompress_none</name>
         <value>0x10a1</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-153">
         <name>__TI_decompress_lzss</name>
         <value>0x9d5</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-15d">
         <name>abort</name>
         <value>0x1119</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-167">
         <name>HOSTexit</name>
         <value>0x111f</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-168">
         <name>C$$EXIT</name>
         <value>0x111e</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-16e">
         <name>__aeabi_fadd</name>
         <value>0x67b</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-16f">
         <name>__addsf3</name>
         <value>0x67b</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-170">
         <name>__aeabi_fsub</name>
         <value>0x671</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-171">
         <name>__subsf3</name>
         <value>0x671</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-177">
         <name>__aeabi_dadd</name>
         <value>0xcb</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-178">
         <name>__adddf3</name>
         <value>0xcb</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-179">
         <name>__aeabi_dsub</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-17a">
         <name>__subdf3</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-180">
         <name>__aeabi_dmul</name>
         <value>0x58d</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-181">
         <name>__muldf3</name>
         <value>0x58d</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-187">
         <name>__muldsi3</name>
         <value>0xe91</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-18d">
         <name>__aeabi_f2d</name>
         <value>0xd61</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-18e">
         <name>__extendsfdf2</name>
         <value>0xd61</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-194">
         <name>__aeabi_f2iz</name>
         <value>0xecd</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-195">
         <name>__fixsfsi</name>
         <value>0xecd</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-19b">
         <name>__aeabi_i2f</name>
         <value>0xda1</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-19c">
         <name>__floatsisf</name>
         <value>0xda1</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-1a2">
         <name>__aeabi_ui2f</name>
         <value>0xf61</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-1a3">
         <name>__floatunsisf</name>
         <value>0xf61</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-1aa">
         <name>__aeabi_d2f</name>
         <value>0xa51</value>
         <object_component_ref idref="oc-11c"/>
      </symbol>
      <symbol id="sm-1ab">
         <name>__truncdfsf2</name>
         <value>0xa51</value>
         <object_component_ref idref="oc-11c"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>__aeabi_fcmpeq</name>
         <value>0xc7d</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-1b2">
         <name>__aeabi_fcmplt</name>
         <value>0xc91</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-1b3">
         <name>__aeabi_fcmple</name>
         <value>0xca5</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-1b4">
         <name>__aeabi_fcmpge</name>
         <value>0xcb9</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-1b5">
         <name>__aeabi_fcmpgt</name>
         <value>0xccd</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-1bb">
         <name>__aeabi_memcpy</name>
         <value>0x1111</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>__aeabi_memcpy4</name>
         <value>0x1111</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>__aeabi_memcpy8</name>
         <value>0x1111</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>__aeabi_uidiv</name>
         <value>0xd21</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>__aeabi_uidivmod</name>
         <value>0xd21</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>__eqsf2</name>
         <value>0xe55</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-1ce">
         <name>__lesf2</name>
         <value>0xe55</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-1cf">
         <name>__ltsf2</name>
         <value>0xe55</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-1d0">
         <name>__nesf2</name>
         <value>0xe55</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-1d1">
         <name>__cmpsf2</name>
         <value>0xe55</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-1d2">
         <name>__gtsf2</name>
         <value>0xddd</value>
         <object_component_ref idref="oc-146"/>
      </symbol>
      <symbol id="sm-1d3">
         <name>__gesf2</name>
         <value>0xddd</value>
         <object_component_ref idref="oc-146"/>
      </symbol>
      <symbol id="sm-1dd">
         <name>__aeabi_idiv0</name>
         <value>0x943</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-1f6">
         <name>memcpy</name>
         <value>0x8a9</value>
         <object_component_ref idref="oc-76"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1fa">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1fb">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
