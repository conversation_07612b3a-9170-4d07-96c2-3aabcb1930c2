#include"pid_Turn.h"









// 改进的左转函数
void pid_TurnLeft()
{
    // 缓进区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);
    
    // 开始转弯
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(turn_duty);
    setPWMRight(turn_duty);   
    delay_ms(385);
    
    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);

    
    count++;
}

//改进右转函数
void pid_TurnRight()
{
    // 缓进区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);

    // 开始转弯
    leftMotorForward();
    rightMotorBackward();
    setPWMLeft(turn_duty);
    setPWMRight(turn_duty);
    delay_ms(385);

    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);


    count++;
}

