#include"pid_Turn.h"

// 函数声明
void enhanced_swing_Search();

// 转弯后寻线对齐函数
void search_AndAlign_Line() {
    uint32_t search_timeout = 0;
    
    // 使用所有传感器寻线，扩大搜索范围
    while(search_timeout < 30) {  // 减少直行时间到300ms
        uint32_t L3 = DL_GPIO_readPins(huidu_L3_PORT, huidu_L3_PIN);
        uint32_t L1 = DL_GPIO_readPins(huidu_L1_PORT, huidu_L1_PIN);
        uint32_t R1 = DL_GPIO_readPins(huidu_R1_PORT, huidu_R1_PIN);
        uint32_t R3 = DL_GPIO_readPins(huidu_R3_PORT, huidu_R3_PIN);
        
        if(L3 || L1 || R1 || R3) {
            // 找到线路，退出寻线
            break;
        }
        
        leftMotorForward();
        rightMotorForward();
        setPWMLeft(25);
        setPWMRight(25);
        delay_ms(10);
        search_timeout++;
    }
    
    // 更积极的摆动寻线
    if(search_timeout >= 30) {
        enhanced_swing_Search();
    }
}

void enhanced_swing_Search() {
    // 大幅度左摆
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(30);
    setPWMRight(40);
    delay_ms(150);
    
    // 检查所有传感器
    uint32_t L3 = DL_GPIO_readPins(huidu_L3_PORT, huidu_L3_PIN);
    uint32_t L1 = DL_GPIO_readPins(huidu_L1_PORT, huidu_L1_PIN);
    uint32_t R1 = DL_GPIO_readPins(huidu_R1_PORT, huidu_R1_PIN);
    uint32_t R3 = DL_GPIO_readPins(huidu_R3_PORT, huidu_R3_PIN);
    
    if(!(L3 || L1 || R1 || R3)) {
        // 大幅度右摆
        leftMotorForward();
        rightMotorBackward();
        setPWMLeft(40);
        setPWMRight(30);
        delay_ms(300);
    }
}



// 改进的左转函数
void pid_TurnLeft()
{
    // 缓进区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);
    
    // 开始转弯
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(turn_duty);
    setPWMRight(turn_duty);   
    delay_ms(385);
    
    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);

    
    count++;
}

//改进右转函数
void pid_TurnRight()
{
    //准备区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);

    // 缓启动
    leftMotorForward();
    rightMotorBackward();
    setPWMLeft(25);
    setPWMRight(25);
    delay_ms(40);
    
    // 主要转弯
    setPWMLeft(40);
    setPWMRight(40);   
    delay_ms(200);
    
    // 减速停止
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(20);
    setPWMRight(20);
    delay_ms(25);
    
    //停止
    leftMotorStop();
    rightMotorStop();
    delay_ms(50);

    // // 转弯后继续前进
    // leftMotorForward();
    // rightMotorForward();
    // setPWMLeft(base_duty);
    // setPWMRight(base_duty);
    // delay_ms(300);


    // 步骤3：转弯后微调寻线
    search_AndAlign_Line();

    count++;
}