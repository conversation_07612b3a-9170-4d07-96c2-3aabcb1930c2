#include"pid_Turn.h"

// 函数声明
void enhanced_swing_Search();

// 转弯后寻线对齐函数
void search_AndAlign_Line() {
    uint32_t align_timeout = 0;

    // 持续调整直到中间传感器都检测到黑线
    while(align_timeout < 100) {  // 最大1秒调整时间
        uint32_t L3 = DL_GPIO_readPins(huidu_L3_PORT, huidu_L3_PIN);
        uint32_t L1 = DL_GPIO_readPins(huidu_L1_PORT, huidu_L1_PIN);
        uint32_t R1 = DL_GPIO_readPins(huidu_R1_PORT, huidu_R1_PIN);
        uint32_t R3 = DL_GPIO_readPins(huidu_R3_PORT, huidu_R3_PIN);

        // 理想状态：中间两个传感器都检测到黑线
        if(L1 && R1) {
            // 对齐完成，退出
            break;
        }

        // 使用所有传感器进行小幅度调整
        if(L3 || L1) {
            // 左侧有线，小幅度右转调整
            leftMotorForward();
            rightMotorForward();
            setPWMLeft(base_duty + 5);
            setPWMRight(base_duty - 5);
        }
        else if(R3 || R1) {
            // 右侧有线，小幅度左转调整
            leftMotorForward();
            rightMotorForward();
            setPWMLeft(base_duty - 5);
            setPWMRight(base_duty + 5);
        }
        else {
            // 没有检测到线，继续前进寻找
            leftMotorForward();
            rightMotorForward();
            setPWMLeft(base_duty);
            setPWMRight(base_duty);
        }

        delay_ms(10);
        align_timeout++;
    }

    // 如果超时仍未对齐，执行摆动寻线
    if(align_timeout >= 100) {
        enhanced_swing_Search();
    }
}

void enhanced_swing_Search() {
    // 大幅度左摆
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(30);
    setPWMRight(40);
    delay_ms(150);
    
    // 检查所有传感器
    uint32_t L3 = DL_GPIO_readPins(huidu_L3_PORT, huidu_L3_PIN);
    uint32_t L1 = DL_GPIO_readPins(huidu_L1_PORT, huidu_L1_PIN);
    uint32_t R1 = DL_GPIO_readPins(huidu_R1_PORT, huidu_R1_PIN);
    uint32_t R3 = DL_GPIO_readPins(huidu_R3_PORT, huidu_R3_PIN);
    
    if(!(L3 || L1 || R1 || R3)) {
        // 大幅度右摆
        leftMotorForward();
        rightMotorBackward();
        setPWMLeft(40);
        setPWMRight(30);
        delay_ms(300);
    }
}



// 改进的左转函数
void pid_TurnLeft()
{
    // 缓进区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);
    
    // 开始转弯
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(turn_duty);
    setPWMRight(turn_duty);   
    delay_ms(385);
    
    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);

    
    count++;
}

//改进右转函数
void pid_TurnRight()
{
    //准备区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);

    // 缓启动
    leftMotorForward();
    rightMotorBackward();
    setPWMLeft(25);
    setPWMRight(25);
    delay_ms(40);
    
    // 主要转弯
    setPWMLeft(40);
    setPWMRight(40);   
    delay_ms(200);
    
    // 减速停止
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(20);
    setPWMRight(20);
    delay_ms(25);
    
    //停止
    leftMotorStop();
    rightMotorStop();
    delay_ms(50);

    // // 转弯后继续前进
    // leftMotorForward();
    // rightMotorForward();
    // setPWMLeft(base_duty);
    // setPWMRight(base_duty);
    // delay_ms(300);


    // 步骤3：转弯后微调寻线
    search_AndAlign_Line();

    count++;
}