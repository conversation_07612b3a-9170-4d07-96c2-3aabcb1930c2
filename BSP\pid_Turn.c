#include"pid_Turn.h"









// 改进的左转函数
void pid_TurnLeft()
{
    // 缓进区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);
    
    // 开始转弯
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(turn_duty);
    setPWMRight(turn_duty);   
    delay_ms(385);
    
    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);

    
    count++;
}

//丝滑右转函数
void pid_TurnRight()
{
    // 步骤1：缓慢前进到转弯位置
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(600);  // 减少前进距离

    // 步骤2：渐进式右转 - 第一阶段（轻微右转）
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty + 3);  // 左轮稍快
    setPWMRight(base_duty - 3); // 右轮稍慢
    delay_ms(150);

    // 步骤3：渐进式右转 - 第二阶段（中等右转）
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty + 8);
    setPWMRight(base_duty - 8);
    delay_ms(120);

    // 步骤4：渐进式右转 - 第三阶段（主要转弯）
    leftMotorForward();
    rightMotorBackward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty - 5);  // 右轮反转但速度较低
    delay_ms(80);

    // 步骤5：转弯收尾 - 逐渐回正
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty + 5);
    setPWMRight(base_duty - 5);
    delay_ms(100);

    // 步骤6：恢复直行
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(200);

    count++;
}

//仿照左转的右转函数
void pid_TurnRight_New()
{
    // 缓进区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);

    // 开始转弯
    leftMotorForward();
    rightMotorBackward();
    setPWMLeft(turn_duty);
    setPWMRight(turn_duty);
    delay_ms(385);

    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);


    count++;
}