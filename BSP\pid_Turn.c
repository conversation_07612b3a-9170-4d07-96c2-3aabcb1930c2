#include"pid_Turn.h"









// 改进的左转函数
void pid_TurnLeft()
{
    // 缓进区
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);
    
    // 开始转弯
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(turn_duty);
    setPWMRight(turn_duty);   
    delay_ms(385);
    
    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);

    
    count++;
}

//改进右转函数
void pid_TurnRight()
{
    // 预转区：让后轮行驶到黑线上
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(710);

    // 分阶段转弯减少偏航
    // 第一阶段：缓启动转弯
    leftMotorForward();
    rightMotorBackward();
    setPWMLeft(turn_duty - 3);  // 稍微降低初始转弯强度
    setPWMRight(turn_duty - 3);
    delay_ms(120);

    // 第二阶段：主要转弯
    setPWMLeft(turn_duty);  // 降低转弯强度
    setPWMRight(turn_duty);
    delay_ms(200);  // 减少主要转弯时间

    // 第三阶段：转弯收尾
    setPWMLeft(turn_duty - 2);  // 减少收尾强度
    setPWMRight(turn_duty - 2);
    delay_ms(65);  // 减少收尾时间

    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty);
    setPWMRight(base_duty);
    delay_ms(300);

    count++;
}

