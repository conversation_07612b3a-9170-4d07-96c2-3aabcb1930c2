#include"pid_Turn.h"









// 改进的左转函数
void pid_TurnLeft()
{
    // 动态计算补偿值：每4圈增加1点补偿，最大补偿10
    power_compensation = (count / 4);
    if(power_compensation > 10) power_compensation = 10;

    // 预转区：让后轮行驶到黑线上
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty + power_compensation);
    setPWMRight(base_duty + power_compensation);
    delay_ms(710);

    // 分阶段转弯减少偏航
    // 第一阶段：缓启动转弯
    leftMotorBackward();
    rightMotorForward();
    setPWMLeft(turn_duty - 3 + power_compensation);  // 加入补偿
    setPWMRight(turn_duty - 3 + power_compensation);
    delay_ms(120);

    // 第二阶段：主要转弯
    setPWMLeft(turn_duty + power_compensation);  // 加入补偿
    setPWMRight(turn_duty + power_compensation);
    delay_ms(200 + power_compensation * 2);  // 时间也适当延长

    // 第三阶段：转弯收尾
    setPWMLeft(turn_duty - 2 + power_compensation);  // 加入补偿
    setPWMRight(turn_duty - 2 + power_compensation);
    delay_ms(65 + power_compensation);  // 时间也适当延长

    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty + power_compensation);
    setPWMRight(base_duty + power_compensation);
    delay_ms(300);


    count++;
}

//改进右转函数
void pid_TurnRight()
{
    // 动态计算补偿值：每4圈增加1点补偿，最大补偿10
    power_compensation = (count / 4);
    if(power_compensation > 10) power_compensation = 10;

    // 预转区：让后轮行驶到黑线上
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty + power_compensation);
    setPWMRight(base_duty + power_compensation);
    delay_ms(710);

    // 分阶段转弯减少偏航
    // 第一阶段：缓启动转弯
    leftMotorForward();
    rightMotorBackward();
    setPWMLeft(turn_duty - 3 + power_compensation);  // 加入补偿
    setPWMRight(turn_duty - 3 + power_compensation);
    delay_ms(120);

    // 第二阶段：主要转弯
    setPWMLeft(turn_duty + power_compensation);  // 加入补偿
    setPWMRight(turn_duty + power_compensation);
    delay_ms(200 + power_compensation * 2);  // 时间也适当延长

    // 第三阶段：转弯收尾
    setPWMLeft(turn_duty - 2 + power_compensation);  // 加入补偿
    setPWMRight(turn_duty - 2 + power_compensation);
    delay_ms(65 + power_compensation);  // 时间也适当延长

    // 转弯后继续前进
    leftMotorForward();
    rightMotorForward();
    setPWMLeft(base_duty + power_compensation);
    setPWMRight(base_duty + power_compensation);
    delay_ms(300);

    count++;
}

