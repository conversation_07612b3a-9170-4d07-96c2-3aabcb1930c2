#include "pwm.h"

void leftMotorForward(void) 
{
    DL_GPIO_setPins(BIN_PORT, BIN_BIN1_PIN );
    DL_GPIO_clearPins( BIN_PORT,BIN_BIN2_PIN);   
}
void leftMotorBackward(void) 
{
    DL_GPIO_clearPins(BIN_PORT, BIN_BIN1_PIN);
    DL_GPIO_setPins(BIN_PORT,BIN_BIN2_PIN ); 
}
void leftMotorStop(void) 
{
    DL_GPIO_clearPins(BIN_PORT, BIN_BIN1_PIN);
    DL_GPIO_clearPins(BIN_PORT, BIN_BIN2_PIN);
}
void rightMotorForward(void)
 {
    DL_GPIO_clearPins(AIN_PORT, AIN_AIN1_PIN);   
    DL_GPIO_setPins(AIN_PORT,AIN_AIN2_PIN); 
}
void rightMotorBackward(void) 
{
    DL_GPIO_setPins(AIN_PORT, AIN_AIN1_PIN); 
    DL_GPIO_clearPins(AIN_PORT, AIN_AIN2_PIN);  
}
void rightMotorStop(void) 
{
    DL_GPIO_clearPins(AIN_PORT,AIN_AIN1_PIN);
    DL_GPIO_clearPins(AIN_PORT, AIN_AIN2_PIN);
}

void setPWMLeft(uint8_t duty) 
{
    uint16_t pwm_duty =3200- 32*duty ; 
    DL_TimerG_setCaptureCompareValue(PWM_0_INST, pwm_duty, DL_TIMER_CC_1_INDEX);
}
void setPWMRight(uint8_t duty)
 {
    uint16_t pwm_duty =3200- 32*duty;  
   DL_TimerG_setCaptureCompareValue(PWM_0_INST, pwm_duty, DL_TIMER_CC_0_INDEX);
}

void delay_ms(uint32_t ms)
{
    while (ms--)
    {
        delay_cycles(32000);
    }
}