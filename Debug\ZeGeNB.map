******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 18:11:18 2025

OUTPUT FILE NAME:   <ZeGeNB.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001165


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001350  0001ecb0  R  X
  SRAM                  20200000   00008000  00000226  00007dda  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001350   00001350    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001250   00001250    r-x .text
  00001310    00001310    00000010   00000010    r-- .rodata
  00001320    00001320    00000030   00000030    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000016   00000000    rw- .data
  20200018    20200018    00000010   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001250     
                  000000c0    000001b8     pid_Turn.o (.text.search_AndAlign_Line)
                  00000278    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000040a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000040c    00000128     empty.o (.text.main)
                  00000534    0000010c     pid_control.o (.text.pid_Calc)
                  00000640    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000744    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000828    000000d8                            : addsf3.S.obj (.text)
                  00000900    000000b0     pid_control.o (.text.TurnMiniLeft)
                  000009b0    000000b0     pid_control.o (.text.TurnMiniright)
                  00000a60    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000afa    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000afc    00000094     pid_Turn.o (.text.pid_TurnRight)
                  00000b90    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00000c20    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000c9c    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000ca0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00000d14    00000074     pid_Turn.o (.text.pid_TurnLeft)
                  00000d88    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000df0    00000068     pwm.o (.text.delay_ms)
                  00000e58    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00000eba    00000002     --HOLE-- [fill = 0]
                  00000ebc    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000efc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000f3c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00000f7c    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  00000fb8    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00000ff4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001030    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000106a    00000002     --HOLE-- [fill = 0]
                  0000106c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000010a6    00000002     --HOLE-- [fill = 0]
                  000010a8    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000010e0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001114    00000028     pid_control.o (.text.TurnStraight)
                  0000113c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00001164    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000118c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000011a8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000011c4    0000001c     pwm.o (.text.setPWMLeft)
                  000011e0    0000001c     pwm.o (.text.setPWMRight)
                  000011fc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001214    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000122a    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000123e    00000002     --HOLE-- [fill = 0]
                  00001240    00000014     pwm.o (.text.leftMotorBackward)
                  00001254    00000014     pwm.o (.text.leftMotorForward)
                  00001268    00000014     pwm.o (.text.leftMotorStop)
                  0000127c    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  0000128e    00000002     --HOLE-- [fill = 0]
                  00001290    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000012a0    00000010     pwm.o (.text.rightMotorBackward)
                  000012b0    00000010     pwm.o (.text.rightMotorForward)
                  000012c0    00000010     pwm.o (.text.rightMotorStop)
                  000012d0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000012da    00000008     pid_control.o (.text.TurnLeft)
                  000012e2    00000008     pid_control.o (.text.TurnRight)
                  000012ea    00000002     --HOLE-- [fill = 0]
                  000012ec    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000012f4    00000006     libc.a : exit.c.obj (.text:abort)
                  000012fa    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000012fe    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001302    0000000e     --HOLE-- [fill = 0]

.cinit     0    00001320    00000030     
                  00001320    0000000c     (.cinit..data.load) [load image, compression = lzss]
                  0000132c    0000000c     (__TI_handler_table)
                  00001338    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001340    00000010     (__TI_cinit_table)

.rodata    0    00001310    00000010     
                  00001310    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00001318    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  0000131b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000016     UNINITIALIZED
                  20200000    00000004     pid_control.o (.data.derivative)
                  20200004    00000004     pid_control.o (.data.integral)
                  20200008    00000004     pid_control.o (.data.last_error)
                  2020000c    00000004     pid_control.o (.data.pid_output)
                  20200010    00000001     empty.o (.data.STOP_COUNT)
                  20200011    00000001     empty.o (.data.base_duty)
                  20200012    00000001     empty.o (.data.count)
                  20200013    00000001     empty.o (.data.max_adjustment)
                  20200014    00000001     empty.o (.data.turn_duty)
                  20200015    00000001     empty.o (.data.turning_flag)

.bss       0    20200018    00000010     UNINITIALIZED
                  20200018    00000004     (.common:L1)
                  2020001c    00000004     (.common:L3)
                  20200020    00000004     (.common:R1)
                  20200024    00000004     (.common:R3)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             384    11        0      
       empty.o                        296    0         22     
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         686    203       22     
                                                              
    .\BSP\
       pid_Turn.o                     704    0         0      
       pid_control.o                  676    0         16     
       pwm.o                          268    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1648   0         16     
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       comparesf2.S.obj               118    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1530   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      48        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4662   251       550    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001340 records: 2, size/record: 8, table size: 16
	.data: load addr=00001320, load size=0000000c bytes, run addr=20200000, run size=00000016 bytes, compression=lzss
	.bss: load addr=00001338, load size=00000008 bytes, run addr=20200018, run size=00000010 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000132c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000040b  ADC0_IRQHandler                 
0000040b  ADC1_IRQHandler                 
0000040b  AES_IRQHandler                  
00000c9c  C$$EXIT                         
0000040b  CANFD0_IRQHandler               
0000040b  DAC0_IRQHandler                 
000012d1  DL_Common_delayCycles           
00000641  DL_Timer_initFourCCPWMMode      
0000118d  DL_Timer_setCaptCompUpdateMethod
000011fd  DL_Timer_setCaptureCompareOutCtl
00001291  DL_Timer_setCaptureCompareValue 
000011a9  DL_Timer_setClockConfig         
0000040b  DMA_IRQHandler                  
0000040b  Default_Handler                 
0000040b  GROUP0_IRQHandler               
0000040b  GROUP1_IRQHandler               
00000c9d  HOSTexit                        
0000040b  HardFault_Handler               
0000040b  I2C0_IRQHandler                 
0000040b  I2C1_IRQHandler                 
20200018  L1                              
2020001c  L3                              
0000040b  NMI_Handler                     
0000040b  PendSV_Handler                  
20200020  R1                              
20200024  R3                              
0000040b  RTC_IRQHandler                  
000012fb  Reset_Handler                   
0000040b  SPI0_IRQHandler                 
0000040b  SPI1_IRQHandler                 
20200010  STOP_COUNT                      
0000040b  SVC_Handler                     
00000d89  SYSCFG_DL_GPIO_init             
00000b91  SYSCFG_DL_PWM_0_init            
00000ebd  SYSCFG_DL_SYSCTL_init           
0000122b  SYSCFG_DL_init                  
000010e1  SYSCFG_DL_initPower             
0000040b  SysTick_Handler                 
0000040b  TIMA0_IRQHandler                
0000040b  TIMA1_IRQHandler                
0000040b  TIMG0_IRQHandler                
0000040b  TIMG12_IRQHandler               
0000040b  TIMG6_IRQHandler                
0000040b  TIMG7_IRQHandler                
0000040b  TIMG8_IRQHandler                
000012db  TurnLeft                        
00000901  TurnMiniLeft                    
000009b1  TurnMiniright                   
000012e3  TurnRight                       
00001115  TurnStraight                    
0000040b  UART0_IRQHandler                
0000040b  UART1_IRQHandler                
0000040b  UART2_IRQHandler                
0000040b  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00001340  __TI_CINIT_Base                 
00001350  __TI_CINIT_Limit                
00001350  __TI_CINIT_Warm                 
0000132c  __TI_Handler_Table_Base         
00001338  __TI_Handler_Table_Limit        
00000ff5  __TI_auto_init_nobinit_nopinit  
00000c21  __TI_decompress_lzss            
0000127d  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00001215  __TI_zero_init_nomemset         
00000283  __adddf3                        
00000833  __addsf3                        
00000ca1  __aeabi_d2f                     
00000283  __aeabi_dadd                    
00000745  __aeabi_dmul                    
00000279  __aeabi_dsub                    
00000f3d  __aeabi_f2d                     
000010a9  __aeabi_f2iz                    
00000833  __aeabi_fadd                    
00000e59  __aeabi_fcmpeq                  
00000e95  __aeabi_fcmpge                  
00000ea9  __aeabi_fcmpgt                  
00000e81  __aeabi_fcmple                  
00000e6d  __aeabi_fcmplt                  
00000829  __aeabi_fsub                    
00000f7d  __aeabi_i2f                     
00000afb  __aeabi_idiv0                   
000012ed  __aeabi_memcpy                  
000012ed  __aeabi_memcpy4                 
000012ed  __aeabi_memcpy8                 
0000113d  __aeabi_ui2f                    
00000efd  __aeabi_uidiv                   
00000efd  __aeabi_uidivmod                
ffffffff  __binit__                       
00001031  __cmpsf2                        
00001031  __eqsf2                         
00000f3d  __extendsfdf2                   
000010a9  __fixsfsi                       
00000f7d  __floatsisf                     
0000113d  __floatunsisf                   
00000fb9  __gesf2                         
00000fb9  __gtsf2                         
00001031  __lesf2                         
00001031  __ltsf2                         
UNDEFED   __mpu_init                      
00000745  __muldf3                        
0000106d  __muldsi3                       
00001031  __nesf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000279  __subdf3                        
00000829  __subsf3                        
00000ca1  __truncdfsf2                    
00001165  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000012ff  _system_pre_init                
000012f5  abort                           
20200011  base_duty                       
ffffffff  binit                           
20200012  count                           
00000df1  delay_ms                        
20200000  derivative                      
20200004  integral                        
00000000  interruptVectors                
20200008  last_error                      
00001241  leftMotorBackward               
00001255  leftMotorForward                
00001269  leftMotorStop                   
0000040d  main                            
20200013  max_adjustment                  
00000a61  memcpy                          
00000535  pid_Calc                        
00000d15  pid_TurnLeft                    
00000afd  pid_TurnRight                   
2020000c  pid_output                      
000012a1  rightMotorBackward              
000012b1  rightMotorForward               
000012c1  rightMotorStop                  
000000c1  search_AndAlign_Line            
000011c5  setPWMLeft                      
000011e1  setPWMRight                     
20200014  turn_duty                       
20200015  turning_flag                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  search_AndAlign_Line            
00000200  __STACK_SIZE                    
00000279  __aeabi_dsub                    
00000279  __subdf3                        
00000283  __adddf3                        
00000283  __aeabi_dadd                    
0000040b  ADC0_IRQHandler                 
0000040b  ADC1_IRQHandler                 
0000040b  AES_IRQHandler                  
0000040b  CANFD0_IRQHandler               
0000040b  DAC0_IRQHandler                 
0000040b  DMA_IRQHandler                  
0000040b  Default_Handler                 
0000040b  GROUP0_IRQHandler               
0000040b  GROUP1_IRQHandler               
0000040b  HardFault_Handler               
0000040b  I2C0_IRQHandler                 
0000040b  I2C1_IRQHandler                 
0000040b  NMI_Handler                     
0000040b  PendSV_Handler                  
0000040b  RTC_IRQHandler                  
0000040b  SPI0_IRQHandler                 
0000040b  SPI1_IRQHandler                 
0000040b  SVC_Handler                     
0000040b  SysTick_Handler                 
0000040b  TIMA0_IRQHandler                
0000040b  TIMA1_IRQHandler                
0000040b  TIMG0_IRQHandler                
0000040b  TIMG12_IRQHandler               
0000040b  TIMG6_IRQHandler                
0000040b  TIMG7_IRQHandler                
0000040b  TIMG8_IRQHandler                
0000040b  UART0_IRQHandler                
0000040b  UART1_IRQHandler                
0000040b  UART2_IRQHandler                
0000040b  UART3_IRQHandler                
0000040d  main                            
00000535  pid_Calc                        
00000641  DL_Timer_initFourCCPWMMode      
00000745  __aeabi_dmul                    
00000745  __muldf3                        
00000829  __aeabi_fsub                    
00000829  __subsf3                        
00000833  __addsf3                        
00000833  __aeabi_fadd                    
00000901  TurnMiniLeft                    
000009b1  TurnMiniright                   
00000a61  memcpy                          
00000afb  __aeabi_idiv0                   
00000afd  pid_TurnRight                   
00000b91  SYSCFG_DL_PWM_0_init            
00000c21  __TI_decompress_lzss            
00000c9c  C$$EXIT                         
00000c9d  HOSTexit                        
00000ca1  __aeabi_d2f                     
00000ca1  __truncdfsf2                    
00000d15  pid_TurnLeft                    
00000d89  SYSCFG_DL_GPIO_init             
00000df1  delay_ms                        
00000e59  __aeabi_fcmpeq                  
00000e6d  __aeabi_fcmplt                  
00000e81  __aeabi_fcmple                  
00000e95  __aeabi_fcmpge                  
00000ea9  __aeabi_fcmpgt                  
00000ebd  SYSCFG_DL_SYSCTL_init           
00000efd  __aeabi_uidiv                   
00000efd  __aeabi_uidivmod                
00000f3d  __aeabi_f2d                     
00000f3d  __extendsfdf2                   
00000f7d  __aeabi_i2f                     
00000f7d  __floatsisf                     
00000fb9  __gesf2                         
00000fb9  __gtsf2                         
00000ff5  __TI_auto_init_nobinit_nopinit  
00001031  __cmpsf2                        
00001031  __eqsf2                         
00001031  __lesf2                         
00001031  __ltsf2                         
00001031  __nesf2                         
0000106d  __muldsi3                       
000010a9  __aeabi_f2iz                    
000010a9  __fixsfsi                       
000010e1  SYSCFG_DL_initPower             
00001115  TurnStraight                    
0000113d  __aeabi_ui2f                    
0000113d  __floatunsisf                   
00001165  _c_int00_noargs                 
0000118d  DL_Timer_setCaptCompUpdateMethod
000011a9  DL_Timer_setClockConfig         
000011c5  setPWMLeft                      
000011e1  setPWMRight                     
000011fd  DL_Timer_setCaptureCompareOutCtl
00001215  __TI_zero_init_nomemset         
0000122b  SYSCFG_DL_init                  
00001241  leftMotorBackward               
00001255  leftMotorForward                
00001269  leftMotorStop                   
0000127d  __TI_decompress_none            
00001291  DL_Timer_setCaptureCompareValue 
000012a1  rightMotorBackward              
000012b1  rightMotorForward               
000012c1  rightMotorStop                  
000012d1  DL_Common_delayCycles           
000012db  TurnLeft                        
000012e3  TurnRight                       
000012ed  __aeabi_memcpy                  
000012ed  __aeabi_memcpy4                 
000012ed  __aeabi_memcpy8                 
000012f5  abort                           
000012fb  Reset_Handler                   
000012ff  _system_pre_init                
0000132c  __TI_Handler_Table_Base         
00001338  __TI_Handler_Table_Limit        
00001340  __TI_CINIT_Base                 
00001350  __TI_CINIT_Limit                
00001350  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  derivative                      
20200004  integral                        
20200008  last_error                      
2020000c  pid_output                      
20200010  STOP_COUNT                      
20200011  base_duty                       
20200012  count                           
20200013  max_adjustment                  
20200014  turn_duty                       
20200015  turning_flag                    
20200018  L1                              
2020001c  L3                              
20200020  R1                              
20200024  R3                              
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[154 symbols]
