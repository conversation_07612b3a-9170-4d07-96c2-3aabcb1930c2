******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 17:30:03 2025

OUTPUT FILE NAME:   <ZeGeNB.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001175


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001360  0001eca0  R  X
  SRAM                  20200000   00008000  00000226  00007dda  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001360   00001360    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001260   00001260    r-x .text
  00001320    00001320    00000010   00000010    r-- .rodata
  00001330    00001330    00000030   00000030    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000016   00000000    rw- .data
  20200018    20200018    00000010   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001260     
                  000000c0    00000210     pid_Turn.o (.text.pid_TurnRight)
                  000002d0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000462    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000464    00000128     empty.o (.text.main)
                  0000058c    0000010c     pid_control.o (.text.pid_Calc)
                  00000698    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000079c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000880    000000d8                            : addsf3.S.obj (.text)
                  00000958    000000b0     pid_control.o (.text.TurnMiniLeft)
                  00000a08    000000b0     pid_control.o (.text.TurnMiniright)
                  00000ab8    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000b52    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000b54    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00000be4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000c60    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00000cd4    00000074     pid_Turn.o (.text.pid_TurnLeft)
                  00000d48    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000db0    00000068     pwm.o (.text.delay_ms)
                  00000e18    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00000e7a    00000002     --HOLE-- [fill = 0]
                  00000e7c    00000050     pid_Turn.o (.text.swing_Search_Line)
                  00000ecc    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000f0c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000f4c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00000f8c    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  00000fc8    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001004    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001040    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000107a    00000002     --HOLE-- [fill = 0]
                  0000107c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000010b6    00000002     --HOLE-- [fill = 0]
                  000010b8    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000010f0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001124    00000028     pid_control.o (.text.TurnStraight)
                  0000114c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00001174    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000119c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000011b8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000011d4    0000001c     pwm.o (.text.setPWMLeft)
                  000011f0    0000001c     pwm.o (.text.setPWMRight)
                  0000120c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001224    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000123a    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000124e    00000002     --HOLE-- [fill = 0]
                  00001250    00000014     pwm.o (.text.leftMotorBackward)
                  00001264    00000014     pwm.o (.text.leftMotorForward)
                  00001278    00000014     pwm.o (.text.leftMotorStop)
                  0000128c    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  0000129e    00000002     --HOLE-- [fill = 0]
                  000012a0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000012b0    00000010     pwm.o (.text.rightMotorBackward)
                  000012c0    00000010     pwm.o (.text.rightMotorForward)
                  000012d0    00000010     pwm.o (.text.rightMotorStop)
                  000012e0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000012ea    00000008     pid_control.o (.text.TurnLeft)
                  000012f2    00000008     pid_control.o (.text.TurnRight)
                  000012fa    00000002     --HOLE-- [fill = 0]
                  000012fc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001304    00000006     libc.a : exit.c.obj (.text:abort)
                  0000130a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000130e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001312    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001316    0000000a     --HOLE-- [fill = 0]

.cinit     0    00001330    00000030     
                  00001330    0000000c     (.cinit..data.load) [load image, compression = lzss]
                  0000133c    0000000c     (__TI_handler_table)
                  00001348    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001350    00000010     (__TI_cinit_table)

.rodata    0    00001320    00000010     
                  00001320    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00001328    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  0000132b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000016     UNINITIALIZED
                  20200000    00000004     pid_control.o (.data.derivative)
                  20200004    00000004     pid_control.o (.data.integral)
                  20200008    00000004     pid_control.o (.data.last_error)
                  2020000c    00000004     pid_control.o (.data.pid_output)
                  20200010    00000001     empty.o (.data.STOP_COUNT)
                  20200011    00000001     empty.o (.data.base_duty)
                  20200012    00000001     empty.o (.data.count)
                  20200013    00000001     empty.o (.data.max_adjustment)
                  20200014    00000001     empty.o (.data.turn_duty)
                  20200015    00000001     empty.o (.data.turning_flag)

.bss       0    20200018    00000010     UNINITIALIZED
                  20200018    00000004     (.common:L1)
                  2020001c    00000004     (.common:L3)
                  20200020    00000004     (.common:R1)
                  20200024    00000004     (.common:R3)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             384    11        0      
       empty.o                        296    0         22     
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         686    203       22     
                                                              
    .\BSP\
       pid_Turn.o                     724    0         0      
       pid_control.o                  676    0         16     
       pwm.o                          268    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1668   0         16     
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       comparesf2.S.obj               118    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1530   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      48        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4682   251       550    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001350 records: 2, size/record: 8, table size: 16
	.data: load addr=00001330, load size=0000000c bytes, run addr=20200000, run size=00000016 bytes, compression=lzss
	.bss: load addr=00001348, load size=00000008 bytes, run addr=20200018, run size=00000010 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000133c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000463  ADC0_IRQHandler                 
00000463  ADC1_IRQHandler                 
00000463  AES_IRQHandler                  
0000130a  C$$EXIT                         
00000463  CANFD0_IRQHandler               
00000463  DAC0_IRQHandler                 
000012e1  DL_Common_delayCycles           
00000699  DL_Timer_initFourCCPWMMode      
0000119d  DL_Timer_setCaptCompUpdateMethod
0000120d  DL_Timer_setCaptureCompareOutCtl
000012a1  DL_Timer_setCaptureCompareValue 
000011b9  DL_Timer_setClockConfig         
00000463  DMA_IRQHandler                  
00000463  Default_Handler                 
00000463  GROUP0_IRQHandler               
00000463  GROUP1_IRQHandler               
0000130b  HOSTexit                        
00000463  HardFault_Handler               
00000463  I2C0_IRQHandler                 
00000463  I2C1_IRQHandler                 
20200018  L1                              
2020001c  L3                              
00000463  NMI_Handler                     
00000463  PendSV_Handler                  
20200020  R1                              
20200024  R3                              
00000463  RTC_IRQHandler                  
0000130f  Reset_Handler                   
00000463  SPI0_IRQHandler                 
00000463  SPI1_IRQHandler                 
20200010  STOP_COUNT                      
00000463  SVC_Handler                     
00000d49  SYSCFG_DL_GPIO_init             
00000b55  SYSCFG_DL_PWM_0_init            
00000ecd  SYSCFG_DL_SYSCTL_init           
0000123b  SYSCFG_DL_init                  
000010f1  SYSCFG_DL_initPower             
00000463  SysTick_Handler                 
00000463  TIMA0_IRQHandler                
00000463  TIMA1_IRQHandler                
00000463  TIMG0_IRQHandler                
00000463  TIMG12_IRQHandler               
00000463  TIMG6_IRQHandler                
00000463  TIMG7_IRQHandler                
00000463  TIMG8_IRQHandler                
000012eb  TurnLeft                        
00000959  TurnMiniLeft                    
00000a09  TurnMiniright                   
000012f3  TurnRight                       
00001125  TurnStraight                    
00000463  UART0_IRQHandler                
00000463  UART1_IRQHandler                
00000463  UART2_IRQHandler                
00000463  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00001350  __TI_CINIT_Base                 
00001360  __TI_CINIT_Limit                
00001360  __TI_CINIT_Warm                 
0000133c  __TI_Handler_Table_Base         
00001348  __TI_Handler_Table_Limit        
00001005  __TI_auto_init_nobinit_nopinit  
00000be5  __TI_decompress_lzss            
0000128d  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00001225  __TI_zero_init_nomemset         
000002db  __adddf3                        
0000088b  __addsf3                        
00000c61  __aeabi_d2f                     
000002db  __aeabi_dadd                    
0000079d  __aeabi_dmul                    
000002d1  __aeabi_dsub                    
00000f4d  __aeabi_f2d                     
000010b9  __aeabi_f2iz                    
0000088b  __aeabi_fadd                    
00000e19  __aeabi_fcmpeq                  
00000e55  __aeabi_fcmpge                  
00000e69  __aeabi_fcmpgt                  
00000e41  __aeabi_fcmple                  
00000e2d  __aeabi_fcmplt                  
00000881  __aeabi_fsub                    
00000f8d  __aeabi_i2f                     
00000b53  __aeabi_idiv0                   
000012fd  __aeabi_memcpy                  
000012fd  __aeabi_memcpy4                 
000012fd  __aeabi_memcpy8                 
0000114d  __aeabi_ui2f                    
00000f0d  __aeabi_uidiv                   
00000f0d  __aeabi_uidivmod                
ffffffff  __binit__                       
00001041  __cmpsf2                        
00001041  __eqsf2                         
00000f4d  __extendsfdf2                   
000010b9  __fixsfsi                       
00000f8d  __floatsisf                     
0000114d  __floatunsisf                   
00000fc9  __gesf2                         
00000fc9  __gtsf2                         
00001041  __lesf2                         
00001041  __ltsf2                         
UNDEFED   __mpu_init                      
0000079d  __muldf3                        
0000107d  __muldsi3                       
00001041  __nesf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000002d1  __subdf3                        
00000881  __subsf3                        
00000c61  __truncdfsf2                    
00001175  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00001313  _system_pre_init                
00001305  abort                           
20200011  base_duty                       
ffffffff  binit                           
20200012  count                           
00000db1  delay_ms                        
20200000  derivative                      
20200004  integral                        
00000000  interruptVectors                
20200008  last_error                      
00001251  leftMotorBackward               
00001265  leftMotorForward                
00001279  leftMotorStop                   
00000465  main                            
20200013  max_adjustment                  
00000ab9  memcpy                          
0000058d  pid_Calc                        
00000cd5  pid_TurnLeft                    
000000c1  pid_TurnRight                   
2020000c  pid_output                      
000012b1  rightMotorBackward              
000012c1  rightMotorForward               
000012d1  rightMotorStop                  
000011d5  setPWMLeft                      
000011f1  setPWMRight                     
00000e7d  swing_Search_Line               
20200014  turn_duty                       
20200015  turning_flag                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  pid_TurnRight                   
00000200  __STACK_SIZE                    
000002d1  __aeabi_dsub                    
000002d1  __subdf3                        
000002db  __adddf3                        
000002db  __aeabi_dadd                    
00000463  ADC0_IRQHandler                 
00000463  ADC1_IRQHandler                 
00000463  AES_IRQHandler                  
00000463  CANFD0_IRQHandler               
00000463  DAC0_IRQHandler                 
00000463  DMA_IRQHandler                  
00000463  Default_Handler                 
00000463  GROUP0_IRQHandler               
00000463  GROUP1_IRQHandler               
00000463  HardFault_Handler               
00000463  I2C0_IRQHandler                 
00000463  I2C1_IRQHandler                 
00000463  NMI_Handler                     
00000463  PendSV_Handler                  
00000463  RTC_IRQHandler                  
00000463  SPI0_IRQHandler                 
00000463  SPI1_IRQHandler                 
00000463  SVC_Handler                     
00000463  SysTick_Handler                 
00000463  TIMA0_IRQHandler                
00000463  TIMA1_IRQHandler                
00000463  TIMG0_IRQHandler                
00000463  TIMG12_IRQHandler               
00000463  TIMG6_IRQHandler                
00000463  TIMG7_IRQHandler                
00000463  TIMG8_IRQHandler                
00000463  UART0_IRQHandler                
00000463  UART1_IRQHandler                
00000463  UART2_IRQHandler                
00000463  UART3_IRQHandler                
00000465  main                            
0000058d  pid_Calc                        
00000699  DL_Timer_initFourCCPWMMode      
0000079d  __aeabi_dmul                    
0000079d  __muldf3                        
00000881  __aeabi_fsub                    
00000881  __subsf3                        
0000088b  __addsf3                        
0000088b  __aeabi_fadd                    
00000959  TurnMiniLeft                    
00000a09  TurnMiniright                   
00000ab9  memcpy                          
00000b53  __aeabi_idiv0                   
00000b55  SYSCFG_DL_PWM_0_init            
00000be5  __TI_decompress_lzss            
00000c61  __aeabi_d2f                     
00000c61  __truncdfsf2                    
00000cd5  pid_TurnLeft                    
00000d49  SYSCFG_DL_GPIO_init             
00000db1  delay_ms                        
00000e19  __aeabi_fcmpeq                  
00000e2d  __aeabi_fcmplt                  
00000e41  __aeabi_fcmple                  
00000e55  __aeabi_fcmpge                  
00000e69  __aeabi_fcmpgt                  
00000e7d  swing_Search_Line               
00000ecd  SYSCFG_DL_SYSCTL_init           
00000f0d  __aeabi_uidiv                   
00000f0d  __aeabi_uidivmod                
00000f4d  __aeabi_f2d                     
00000f4d  __extendsfdf2                   
00000f8d  __aeabi_i2f                     
00000f8d  __floatsisf                     
00000fc9  __gesf2                         
00000fc9  __gtsf2                         
00001005  __TI_auto_init_nobinit_nopinit  
00001041  __cmpsf2                        
00001041  __eqsf2                         
00001041  __lesf2                         
00001041  __ltsf2                         
00001041  __nesf2                         
0000107d  __muldsi3                       
000010b9  __aeabi_f2iz                    
000010b9  __fixsfsi                       
000010f1  SYSCFG_DL_initPower             
00001125  TurnStraight                    
0000114d  __aeabi_ui2f                    
0000114d  __floatunsisf                   
00001175  _c_int00_noargs                 
0000119d  DL_Timer_setCaptCompUpdateMethod
000011b9  DL_Timer_setClockConfig         
000011d5  setPWMLeft                      
000011f1  setPWMRight                     
0000120d  DL_Timer_setCaptureCompareOutCtl
00001225  __TI_zero_init_nomemset         
0000123b  SYSCFG_DL_init                  
00001251  leftMotorBackward               
00001265  leftMotorForward                
00001279  leftMotorStop                   
0000128d  __TI_decompress_none            
000012a1  DL_Timer_setCaptureCompareValue 
000012b1  rightMotorBackward              
000012c1  rightMotorForward               
000012d1  rightMotorStop                  
000012e1  DL_Common_delayCycles           
000012eb  TurnLeft                        
000012f3  TurnRight                       
000012fd  __aeabi_memcpy                  
000012fd  __aeabi_memcpy4                 
000012fd  __aeabi_memcpy8                 
00001305  abort                           
0000130a  C$$EXIT                         
0000130b  HOSTexit                        
0000130f  Reset_Handler                   
00001313  _system_pre_init                
0000133c  __TI_Handler_Table_Base         
00001348  __TI_Handler_Table_Limit        
00001350  __TI_CINIT_Base                 
00001360  __TI_CINIT_Limit                
00001360  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  derivative                      
20200004  integral                        
20200008  last_error                      
2020000c  pid_output                      
20200010  STOP_COUNT                      
20200011  base_duty                       
20200012  count                           
20200013  max_adjustment                  
20200014  turn_duty                       
20200015  turning_flag                    
20200018  L1                              
2020001c  L3                              
20200020  R1                              
20200024  R3                              
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[154 symbols]
