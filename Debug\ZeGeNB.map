******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 18:52:21 2025

OUTPUT FILE NAME:   <ZeGeNB.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000010a1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001288  0001ed78  R  X
  SRAM                  20200000   00008000  00000227  00007dd9  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001288   00001288    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001180   00001180    r-x .text
  00001240    00001240    00000010   00000010    r-- .rodata
  00001250    00001250    00000038   00000038    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000017   00000000    rw- .data
  20200018    20200018    00000010   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001180     
                  000000c0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000252    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000254    00000144     empty.o (.text.main)
                  00000398    00000110     pid_control.o (.text.pid_Calc)
                  000004a8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000005ac    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000690    000000d8                            : addsf3.S.obj (.text)
                  00000768    000000b0     pid_control.o (.text.TurnMiniLeft)
                  00000818    000000b0     pid_control.o (.text.TurnMiniright)
                  000008c8    000000a4     pid_Turn.o (.text.pid_TurnLeft)
                  0000096c    000000a4     pid_Turn.o (.text.pid_TurnRight)
                  00000a10    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000aaa    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000aac    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00000b3c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00000bc8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000c44    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000c4e    00000002     --HOLE-- [fill = 0]
                  00000c50    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00000cc4    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000d2c    00000068     pwm.o (.text.delay_ms)
                  00000d94    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00000df6    00000002     --HOLE-- [fill = 0]
                  00000df8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000e38    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000e78    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00000eb8    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  00000ef4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00000f30    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000f6c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00000fa6    00000002     --HOLE-- [fill = 0]
                  00000fa8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00000fe2    00000002     --HOLE-- [fill = 0]
                  00000fe4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  0000101c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001050    00000028     pid_control.o (.text.TurnStraight)
                  00001078    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000010a0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000010c8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000010e4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001100    0000001c     pwm.o (.text.setPWMLeft)
                  0000111c    0000001c     pwm.o (.text.setPWMRight)
                  00001138    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001150    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001166    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000117a    00000002     --HOLE-- [fill = 0]
                  0000117c    00000014     pwm.o (.text.leftMotorBackward)
                  00001190    00000014     pwm.o (.text.leftMotorForward)
                  000011a4    00000014     pwm.o (.text.leftMotorStop)
                  000011b8    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000011ca    00000002     --HOLE-- [fill = 0]
                  000011cc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000011dc    00000010     pwm.o (.text.rightMotorBackward)
                  000011ec    00000010     pwm.o (.text.rightMotorForward)
                  000011fc    00000010     pwm.o (.text.rightMotorStop)
                  0000120c    00000008     pid_control.o (.text.TurnLeft)
                  00001214    00000008     pid_control.o (.text.TurnRight)
                  0000121c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001224    00000006     libc.a : exit.c.obj (.text:abort)
                  0000122a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000122e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001232    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001236    0000000a     --HOLE-- [fill = 0]

.cinit     0    00001250    00000038     
                  00001250    0000000e     (.cinit..data.load) [load image, compression = lzss]
                  0000125e    00000002     --HOLE-- [fill = 0]
                  00001260    0000000c     (__TI_handler_table)
                  0000126c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001274    00000010     (__TI_cinit_table)
                  00001284    00000004     --HOLE-- [fill = 0]

.rodata    0    00001240    00000010     
                  00001240    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00001248    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  0000124b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000017     UNINITIALIZED
                  20200000    00000004     pid_control.o (.data.derivative)
                  20200004    00000004     pid_control.o (.data.integral)
                  20200008    00000004     pid_control.o (.data.last_error)
                  2020000c    00000004     pid_control.o (.data.pid_output)
                  20200010    00000001     empty.o (.data.STOP_COUNT)
                  20200011    00000001     empty.o (.data.base_duty)
                  20200012    00000001     empty.o (.data.count)
                  20200013    00000001     empty.o (.data.max_adjustment)
                  20200014    00000001     empty.o (.data.turn_assist_mode)
                  20200015    00000001     empty.o (.data.turn_duty)
                  20200016    00000001     empty.o (.data.turning_flag)

.bss       0    20200018    00000010     UNINITIALIZED
                  20200018    00000004     (.common:L1)
                  2020001c    00000004     (.common:L3)
                  20200020    00000004     (.common:R1)
                  20200024    00000004     (.common:R3)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             384    11        0      
       empty.o                        324    0         23     
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         714    203       23     
                                                              
    .\BSP\
       pid_control.o                  680    0         16     
       pid_Turn.o                     328    0         0      
       pwm.o                          268    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1276   0         16     
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1670   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      50        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4458   253       551    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001274 records: 2, size/record: 8, table size: 16
	.data: load addr=00001250, load size=0000000e bytes, run addr=20200000, run size=00000017 bytes, compression=lzss
	.bss: load addr=0000126c, load size=00000008 bytes, run addr=20200018, run size=00000010 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001260 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000253  ADC0_IRQHandler                 
00000253  ADC1_IRQHandler                 
00000253  AES_IRQHandler                  
0000122a  C$$EXIT                         
00000253  CANFD0_IRQHandler               
00000253  DAC0_IRQHandler                 
00000c45  DL_Common_delayCycles           
000004a9  DL_Timer_initFourCCPWMMode      
000010c9  DL_Timer_setCaptCompUpdateMethod
00001139  DL_Timer_setCaptureCompareOutCtl
000011cd  DL_Timer_setCaptureCompareValue 
000010e5  DL_Timer_setClockConfig         
00000253  DMA_IRQHandler                  
00000253  Default_Handler                 
00000253  GROUP0_IRQHandler               
00000253  GROUP1_IRQHandler               
0000122b  HOSTexit                        
00000253  HardFault_Handler               
00000253  I2C0_IRQHandler                 
00000253  I2C1_IRQHandler                 
20200018  L1                              
2020001c  L3                              
00000253  NMI_Handler                     
00000253  PendSV_Handler                  
20200020  R1                              
20200024  R3                              
00000253  RTC_IRQHandler                  
0000122f  Reset_Handler                   
00000253  SPI0_IRQHandler                 
00000253  SPI1_IRQHandler                 
20200010  STOP_COUNT                      
00000253  SVC_Handler                     
00000cc5  SYSCFG_DL_GPIO_init             
00000aad  SYSCFG_DL_PWM_0_init            
00000df9  SYSCFG_DL_SYSCTL_init           
00001167  SYSCFG_DL_init                  
0000101d  SYSCFG_DL_initPower             
00000253  SysTick_Handler                 
00000253  TIMA0_IRQHandler                
00000253  TIMA1_IRQHandler                
00000253  TIMG0_IRQHandler                
00000253  TIMG12_IRQHandler               
00000253  TIMG6_IRQHandler                
00000253  TIMG7_IRQHandler                
00000253  TIMG8_IRQHandler                
0000120d  TurnLeft                        
00000769  TurnMiniLeft                    
00000819  TurnMiniright                   
00001215  TurnRight                       
00001051  TurnStraight                    
00000253  UART0_IRQHandler                
00000253  UART1_IRQHandler                
00000253  UART2_IRQHandler                
00000253  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00001274  __TI_CINIT_Base                 
00001284  __TI_CINIT_Limit                
00001284  __TI_CINIT_Warm                 
00001260  __TI_Handler_Table_Base         
0000126c  __TI_Handler_Table_Limit        
00000f31  __TI_auto_init_nobinit_nopinit  
00000bc9  __TI_decompress_lzss            
000011b9  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00001151  __TI_zero_init_nomemset         
000000cb  __adddf3                        
0000069b  __addsf3                        
00000c51  __aeabi_d2f                     
000000cb  __aeabi_dadd                    
000005ad  __aeabi_dmul                    
000000c1  __aeabi_dsub                    
00000e79  __aeabi_f2d                     
00000fe5  __aeabi_f2iz                    
0000069b  __aeabi_fadd                    
00000d95  __aeabi_fcmpeq                  
00000dd1  __aeabi_fcmpge                  
00000de5  __aeabi_fcmpgt                  
00000dbd  __aeabi_fcmple                  
00000da9  __aeabi_fcmplt                  
00000b3d  __aeabi_fmul                    
00000691  __aeabi_fsub                    
00000eb9  __aeabi_i2f                     
00000aab  __aeabi_idiv0                   
0000121d  __aeabi_memcpy                  
0000121d  __aeabi_memcpy4                 
0000121d  __aeabi_memcpy8                 
00001079  __aeabi_ui2f                    
00000e39  __aeabi_uidiv                   
00000e39  __aeabi_uidivmod                
ffffffff  __binit__                       
00000f6d  __cmpsf2                        
00000f6d  __eqsf2                         
00000e79  __extendsfdf2                   
00000fe5  __fixsfsi                       
00000eb9  __floatsisf                     
00001079  __floatunsisf                   
00000ef5  __gesf2                         
00000ef5  __gtsf2                         
00000f6d  __lesf2                         
00000f6d  __ltsf2                         
UNDEFED   __mpu_init                      
000005ad  __muldf3                        
00000fa9  __muldsi3                       
00000b3d  __mulsf3                        
00000f6d  __nesf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000000c1  __subdf3                        
00000691  __subsf3                        
00000c51  __truncdfsf2                    
000010a1  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00001233  _system_pre_init                
00001225  abort                           
20200011  base_duty                       
ffffffff  binit                           
20200012  count                           
00000d2d  delay_ms                        
20200000  derivative                      
20200004  integral                        
00000000  interruptVectors                
20200008  last_error                      
0000117d  leftMotorBackward               
00001191  leftMotorForward                
000011a5  leftMotorStop                   
00000255  main                            
20200013  max_adjustment                  
00000a11  memcpy                          
00000399  pid_Calc                        
000008c9  pid_TurnLeft                    
0000096d  pid_TurnRight                   
2020000c  pid_output                      
000011dd  rightMotorBackward              
000011ed  rightMotorForward               
000011fd  rightMotorStop                  
00001101  setPWMLeft                      
0000111d  setPWMRight                     
20200014  turn_assist_mode                
20200015  turn_duty                       
20200016  turning_flag                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  __aeabi_dsub                    
000000c1  __subdf3                        
000000cb  __adddf3                        
000000cb  __aeabi_dadd                    
00000200  __STACK_SIZE                    
00000253  ADC0_IRQHandler                 
00000253  ADC1_IRQHandler                 
00000253  AES_IRQHandler                  
00000253  CANFD0_IRQHandler               
00000253  DAC0_IRQHandler                 
00000253  DMA_IRQHandler                  
00000253  Default_Handler                 
00000253  GROUP0_IRQHandler               
00000253  GROUP1_IRQHandler               
00000253  HardFault_Handler               
00000253  I2C0_IRQHandler                 
00000253  I2C1_IRQHandler                 
00000253  NMI_Handler                     
00000253  PendSV_Handler                  
00000253  RTC_IRQHandler                  
00000253  SPI0_IRQHandler                 
00000253  SPI1_IRQHandler                 
00000253  SVC_Handler                     
00000253  SysTick_Handler                 
00000253  TIMA0_IRQHandler                
00000253  TIMA1_IRQHandler                
00000253  TIMG0_IRQHandler                
00000253  TIMG12_IRQHandler               
00000253  TIMG6_IRQHandler                
00000253  TIMG7_IRQHandler                
00000253  TIMG8_IRQHandler                
00000253  UART0_IRQHandler                
00000253  UART1_IRQHandler                
00000253  UART2_IRQHandler                
00000253  UART3_IRQHandler                
00000255  main                            
00000399  pid_Calc                        
000004a9  DL_Timer_initFourCCPWMMode      
000005ad  __aeabi_dmul                    
000005ad  __muldf3                        
00000691  __aeabi_fsub                    
00000691  __subsf3                        
0000069b  __addsf3                        
0000069b  __aeabi_fadd                    
00000769  TurnMiniLeft                    
00000819  TurnMiniright                   
000008c9  pid_TurnLeft                    
0000096d  pid_TurnRight                   
00000a11  memcpy                          
00000aab  __aeabi_idiv0                   
00000aad  SYSCFG_DL_PWM_0_init            
00000b3d  __aeabi_fmul                    
00000b3d  __mulsf3                        
00000bc9  __TI_decompress_lzss            
00000c45  DL_Common_delayCycles           
00000c51  __aeabi_d2f                     
00000c51  __truncdfsf2                    
00000cc5  SYSCFG_DL_GPIO_init             
00000d2d  delay_ms                        
00000d95  __aeabi_fcmpeq                  
00000da9  __aeabi_fcmplt                  
00000dbd  __aeabi_fcmple                  
00000dd1  __aeabi_fcmpge                  
00000de5  __aeabi_fcmpgt                  
00000df9  SYSCFG_DL_SYSCTL_init           
00000e39  __aeabi_uidiv                   
00000e39  __aeabi_uidivmod                
00000e79  __aeabi_f2d                     
00000e79  __extendsfdf2                   
00000eb9  __aeabi_i2f                     
00000eb9  __floatsisf                     
00000ef5  __gesf2                         
00000ef5  __gtsf2                         
00000f31  __TI_auto_init_nobinit_nopinit  
00000f6d  __cmpsf2                        
00000f6d  __eqsf2                         
00000f6d  __lesf2                         
00000f6d  __ltsf2                         
00000f6d  __nesf2                         
00000fa9  __muldsi3                       
00000fe5  __aeabi_f2iz                    
00000fe5  __fixsfsi                       
0000101d  SYSCFG_DL_initPower             
00001051  TurnStraight                    
00001079  __aeabi_ui2f                    
00001079  __floatunsisf                   
000010a1  _c_int00_noargs                 
000010c9  DL_Timer_setCaptCompUpdateMethod
000010e5  DL_Timer_setClockConfig         
00001101  setPWMLeft                      
0000111d  setPWMRight                     
00001139  DL_Timer_setCaptureCompareOutCtl
00001151  __TI_zero_init_nomemset         
00001167  SYSCFG_DL_init                  
0000117d  leftMotorBackward               
00001191  leftMotorForward                
000011a5  leftMotorStop                   
000011b9  __TI_decompress_none            
000011cd  DL_Timer_setCaptureCompareValue 
000011dd  rightMotorBackward              
000011ed  rightMotorForward               
000011fd  rightMotorStop                  
0000120d  TurnLeft                        
00001215  TurnRight                       
0000121d  __aeabi_memcpy                  
0000121d  __aeabi_memcpy4                 
0000121d  __aeabi_memcpy8                 
00001225  abort                           
0000122a  C$$EXIT                         
0000122b  HOSTexit                        
0000122f  Reset_Handler                   
00001233  _system_pre_init                
00001260  __TI_Handler_Table_Base         
0000126c  __TI_Handler_Table_Limit        
00001274  __TI_CINIT_Base                 
00001284  __TI_CINIT_Limit                
00001284  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  derivative                      
20200004  integral                        
20200008  last_error                      
2020000c  pid_output                      
20200010  STOP_COUNT                      
20200011  base_duty                       
20200012  count                           
20200013  max_adjustment                  
20200014  turn_assist_mode                
20200015  turn_duty                       
20200016  turning_flag                    
20200018  L1                              
2020001c  L3                              
20200020  R1                              
20200024  R3                              
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[156 symbols]
