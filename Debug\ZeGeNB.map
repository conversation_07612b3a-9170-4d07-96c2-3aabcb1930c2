******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 17:58:47 2025

OUTPUT FILE NAME:   <ZeGeNB.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000124d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001430  0001ebd0  R  X
  SRAM                  20200000   00008000  00000226  00007dda  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001430   00001430    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001330   00001330    r-x .text
  000013f0    000013f0    00000010   00000010    r-- .rodata
  00001400    00001400    00000030   00000030    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000016   00000000    rw- .data
  20200018    20200018    00000010   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001330     
                  000000c0    00000234     pid_Turn.o (.text.search_AndAlign_Line)
                  000002f4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000486    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000488    00000128     empty.o (.text.main)
                  000005b0    0000010c     pid_control.o (.text.pid_Calc)
                  000006bc    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000007c0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000008a4    000000d8                            : addsf3.S.obj (.text)
                  0000097c    000000b0     pid_control.o (.text.TurnMiniLeft)
                  00000a2c    000000b0     pid_control.o (.text.TurnMiniright)
                  00000adc    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000b76    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000b78    00000094     pid_Turn.o (.text.pid_TurnRight)
                  00000c0c    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00000c9c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000d18    00000008     pid_control.o (.text.TurnLeft)
                  00000d20    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00000d94    00000074     pid_Turn.o (.text.pid_TurnLeft)
                  00000e08    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000e70    00000068     pwm.o (.text.delay_ms)
                  00000ed8    00000068     pid_Turn.o (.text.enhanced_swing_Search)
                  00000f40    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00000fa2    00000002     --HOLE-- [fill = 0]
                  00000fa4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000fe4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001024    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001064    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  000010a0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000010dc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001118    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001152    00000002     --HOLE-- [fill = 0]
                  00001154    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000118e    00000002     --HOLE-- [fill = 0]
                  00001190    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000011c8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000011fc    00000028     pid_control.o (.text.TurnStraight)
                  00001224    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0000124c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001274    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001290    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000012ac    0000001c     pwm.o (.text.setPWMLeft)
                  000012c8    0000001c     pwm.o (.text.setPWMRight)
                  000012e4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000012fc    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001312    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001326    00000002     --HOLE-- [fill = 0]
                  00001328    00000014     pwm.o (.text.leftMotorBackward)
                  0000133c    00000014     pwm.o (.text.leftMotorForward)
                  00001350    00000014     pwm.o (.text.leftMotorStop)
                  00001364    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00001376    00000002     --HOLE-- [fill = 0]
                  00001378    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00001388    00000010     pwm.o (.text.rightMotorBackward)
                  00001398    00000010     pwm.o (.text.rightMotorForward)
                  000013a8    00000010     pwm.o (.text.rightMotorStop)
                  000013b8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000013c2    00000008     pid_control.o (.text.TurnRight)
                  000013ca    00000002     --HOLE-- [fill = 0]
                  000013cc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000013d4    00000006     libc.a : exit.c.obj (.text:abort)
                  000013da    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000013de    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000013e2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000013e6    0000000a     --HOLE-- [fill = 0]

.cinit     0    00001400    00000030     
                  00001400    0000000c     (.cinit..data.load) [load image, compression = lzss]
                  0000140c    0000000c     (__TI_handler_table)
                  00001418    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001420    00000010     (__TI_cinit_table)

.rodata    0    000013f0    00000010     
                  000013f0    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  000013f8    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  000013fb    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000016     UNINITIALIZED
                  20200000    00000004     pid_control.o (.data.derivative)
                  20200004    00000004     pid_control.o (.data.integral)
                  20200008    00000004     pid_control.o (.data.last_error)
                  2020000c    00000004     pid_control.o (.data.pid_output)
                  20200010    00000001     empty.o (.data.STOP_COUNT)
                  20200011    00000001     empty.o (.data.base_duty)
                  20200012    00000001     empty.o (.data.count)
                  20200013    00000001     empty.o (.data.max_adjustment)
                  20200014    00000001     empty.o (.data.turn_duty)
                  20200015    00000001     empty.o (.data.turning_flag)

.bss       0    20200018    00000010     UNINITIALIZED
                  20200018    00000004     (.common:L1)
                  2020001c    00000004     (.common:L3)
                  20200020    00000004     (.common:R1)
                  20200024    00000004     (.common:R3)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             384    11        0      
       empty.o                        296    0         22     
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         686    203       22     
                                                              
    .\BSP\
       pid_Turn.o                     932    0         0      
       pid_control.o                  676    0         16     
       pwm.o                          268    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1876   0         16     
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       comparesf2.S.obj               118    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1530   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      48        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4890   251       550    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001420 records: 2, size/record: 8, table size: 16
	.data: load addr=00001400, load size=0000000c bytes, run addr=20200000, run size=00000016 bytes, compression=lzss
	.bss: load addr=00001418, load size=00000008 bytes, run addr=20200018, run size=00000010 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000140c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000487  ADC0_IRQHandler                 
00000487  ADC1_IRQHandler                 
00000487  AES_IRQHandler                  
000013da  C$$EXIT                         
00000487  CANFD0_IRQHandler               
00000487  DAC0_IRQHandler                 
000013b9  DL_Common_delayCycles           
000006bd  DL_Timer_initFourCCPWMMode      
00001275  DL_Timer_setCaptCompUpdateMethod
000012e5  DL_Timer_setCaptureCompareOutCtl
00001379  DL_Timer_setCaptureCompareValue 
00001291  DL_Timer_setClockConfig         
00000487  DMA_IRQHandler                  
00000487  Default_Handler                 
00000487  GROUP0_IRQHandler               
00000487  GROUP1_IRQHandler               
000013db  HOSTexit                        
00000487  HardFault_Handler               
00000487  I2C0_IRQHandler                 
00000487  I2C1_IRQHandler                 
20200018  L1                              
2020001c  L3                              
00000487  NMI_Handler                     
00000487  PendSV_Handler                  
20200020  R1                              
20200024  R3                              
00000487  RTC_IRQHandler                  
000013df  Reset_Handler                   
00000487  SPI0_IRQHandler                 
00000487  SPI1_IRQHandler                 
20200010  STOP_COUNT                      
00000487  SVC_Handler                     
00000e09  SYSCFG_DL_GPIO_init             
00000c0d  SYSCFG_DL_PWM_0_init            
00000fa5  SYSCFG_DL_SYSCTL_init           
00001313  SYSCFG_DL_init                  
000011c9  SYSCFG_DL_initPower             
00000487  SysTick_Handler                 
00000487  TIMA0_IRQHandler                
00000487  TIMA1_IRQHandler                
00000487  TIMG0_IRQHandler                
00000487  TIMG12_IRQHandler               
00000487  TIMG6_IRQHandler                
00000487  TIMG7_IRQHandler                
00000487  TIMG8_IRQHandler                
00000d19  TurnLeft                        
0000097d  TurnMiniLeft                    
00000a2d  TurnMiniright                   
000013c3  TurnRight                       
000011fd  TurnStraight                    
00000487  UART0_IRQHandler                
00000487  UART1_IRQHandler                
00000487  UART2_IRQHandler                
00000487  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00001420  __TI_CINIT_Base                 
00001430  __TI_CINIT_Limit                
00001430  __TI_CINIT_Warm                 
0000140c  __TI_Handler_Table_Base         
00001418  __TI_Handler_Table_Limit        
000010dd  __TI_auto_init_nobinit_nopinit  
00000c9d  __TI_decompress_lzss            
00001365  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000012fd  __TI_zero_init_nomemset         
000002ff  __adddf3                        
000008af  __addsf3                        
00000d21  __aeabi_d2f                     
000002ff  __aeabi_dadd                    
000007c1  __aeabi_dmul                    
000002f5  __aeabi_dsub                    
00001025  __aeabi_f2d                     
00001191  __aeabi_f2iz                    
000008af  __aeabi_fadd                    
00000f41  __aeabi_fcmpeq                  
00000f7d  __aeabi_fcmpge                  
00000f91  __aeabi_fcmpgt                  
00000f69  __aeabi_fcmple                  
00000f55  __aeabi_fcmplt                  
000008a5  __aeabi_fsub                    
00001065  __aeabi_i2f                     
00000b77  __aeabi_idiv0                   
000013cd  __aeabi_memcpy                  
000013cd  __aeabi_memcpy4                 
000013cd  __aeabi_memcpy8                 
00001225  __aeabi_ui2f                    
00000fe5  __aeabi_uidiv                   
00000fe5  __aeabi_uidivmod                
ffffffff  __binit__                       
00001119  __cmpsf2                        
00001119  __eqsf2                         
00001025  __extendsfdf2                   
00001191  __fixsfsi                       
00001065  __floatsisf                     
00001225  __floatunsisf                   
000010a1  __gesf2                         
000010a1  __gtsf2                         
00001119  __lesf2                         
00001119  __ltsf2                         
UNDEFED   __mpu_init                      
000007c1  __muldf3                        
00001155  __muldsi3                       
00001119  __nesf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000002f5  __subdf3                        
000008a5  __subsf3                        
00000d21  __truncdfsf2                    
0000124d  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000013e3  _system_pre_init                
000013d5  abort                           
20200011  base_duty                       
ffffffff  binit                           
20200012  count                           
00000e71  delay_ms                        
20200000  derivative                      
00000ed9  enhanced_swing_Search           
20200004  integral                        
00000000  interruptVectors                
20200008  last_error                      
00001329  leftMotorBackward               
0000133d  leftMotorForward                
00001351  leftMotorStop                   
00000489  main                            
20200013  max_adjustment                  
00000add  memcpy                          
000005b1  pid_Calc                        
00000d95  pid_TurnLeft                    
00000b79  pid_TurnRight                   
2020000c  pid_output                      
00001389  rightMotorBackward              
00001399  rightMotorForward               
000013a9  rightMotorStop                  
000000c1  search_AndAlign_Line            
000012ad  setPWMLeft                      
000012c9  setPWMRight                     
20200014  turn_duty                       
20200015  turning_flag                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  search_AndAlign_Line            
00000200  __STACK_SIZE                    
000002f5  __aeabi_dsub                    
000002f5  __subdf3                        
000002ff  __adddf3                        
000002ff  __aeabi_dadd                    
00000487  ADC0_IRQHandler                 
00000487  ADC1_IRQHandler                 
00000487  AES_IRQHandler                  
00000487  CANFD0_IRQHandler               
00000487  DAC0_IRQHandler                 
00000487  DMA_IRQHandler                  
00000487  Default_Handler                 
00000487  GROUP0_IRQHandler               
00000487  GROUP1_IRQHandler               
00000487  HardFault_Handler               
00000487  I2C0_IRQHandler                 
00000487  I2C1_IRQHandler                 
00000487  NMI_Handler                     
00000487  PendSV_Handler                  
00000487  RTC_IRQHandler                  
00000487  SPI0_IRQHandler                 
00000487  SPI1_IRQHandler                 
00000487  SVC_Handler                     
00000487  SysTick_Handler                 
00000487  TIMA0_IRQHandler                
00000487  TIMA1_IRQHandler                
00000487  TIMG0_IRQHandler                
00000487  TIMG12_IRQHandler               
00000487  TIMG6_IRQHandler                
00000487  TIMG7_IRQHandler                
00000487  TIMG8_IRQHandler                
00000487  UART0_IRQHandler                
00000487  UART1_IRQHandler                
00000487  UART2_IRQHandler                
00000487  UART3_IRQHandler                
00000489  main                            
000005b1  pid_Calc                        
000006bd  DL_Timer_initFourCCPWMMode      
000007c1  __aeabi_dmul                    
000007c1  __muldf3                        
000008a5  __aeabi_fsub                    
000008a5  __subsf3                        
000008af  __addsf3                        
000008af  __aeabi_fadd                    
0000097d  TurnMiniLeft                    
00000a2d  TurnMiniright                   
00000add  memcpy                          
00000b77  __aeabi_idiv0                   
00000b79  pid_TurnRight                   
00000c0d  SYSCFG_DL_PWM_0_init            
00000c9d  __TI_decompress_lzss            
00000d19  TurnLeft                        
00000d21  __aeabi_d2f                     
00000d21  __truncdfsf2                    
00000d95  pid_TurnLeft                    
00000e09  SYSCFG_DL_GPIO_init             
00000e71  delay_ms                        
00000ed9  enhanced_swing_Search           
00000f41  __aeabi_fcmpeq                  
00000f55  __aeabi_fcmplt                  
00000f69  __aeabi_fcmple                  
00000f7d  __aeabi_fcmpge                  
00000f91  __aeabi_fcmpgt                  
00000fa5  SYSCFG_DL_SYSCTL_init           
00000fe5  __aeabi_uidiv                   
00000fe5  __aeabi_uidivmod                
00001025  __aeabi_f2d                     
00001025  __extendsfdf2                   
00001065  __aeabi_i2f                     
00001065  __floatsisf                     
000010a1  __gesf2                         
000010a1  __gtsf2                         
000010dd  __TI_auto_init_nobinit_nopinit  
00001119  __cmpsf2                        
00001119  __eqsf2                         
00001119  __lesf2                         
00001119  __ltsf2                         
00001119  __nesf2                         
00001155  __muldsi3                       
00001191  __aeabi_f2iz                    
00001191  __fixsfsi                       
000011c9  SYSCFG_DL_initPower             
000011fd  TurnStraight                    
00001225  __aeabi_ui2f                    
00001225  __floatunsisf                   
0000124d  _c_int00_noargs                 
00001275  DL_Timer_setCaptCompUpdateMethod
00001291  DL_Timer_setClockConfig         
000012ad  setPWMLeft                      
000012c9  setPWMRight                     
000012e5  DL_Timer_setCaptureCompareOutCtl
000012fd  __TI_zero_init_nomemset         
00001313  SYSCFG_DL_init                  
00001329  leftMotorBackward               
0000133d  leftMotorForward                
00001351  leftMotorStop                   
00001365  __TI_decompress_none            
00001379  DL_Timer_setCaptureCompareValue 
00001389  rightMotorBackward              
00001399  rightMotorForward               
000013a9  rightMotorStop                  
000013b9  DL_Common_delayCycles           
000013c3  TurnRight                       
000013cd  __aeabi_memcpy                  
000013cd  __aeabi_memcpy4                 
000013cd  __aeabi_memcpy8                 
000013d5  abort                           
000013da  C$$EXIT                         
000013db  HOSTexit                        
000013df  Reset_Handler                   
000013e3  _system_pre_init                
0000140c  __TI_Handler_Table_Base         
00001418  __TI_Handler_Table_Limit        
00001420  __TI_CINIT_Base                 
00001430  __TI_CINIT_Limit                
00001430  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  derivative                      
20200004  integral                        
20200008  last_error                      
2020000c  pid_output                      
20200010  STOP_COUNT                      
20200011  base_duty                       
20200012  count                           
20200013  max_adjustment                  
20200014  turn_duty                       
20200015  turning_flag                    
20200018  L1                              
2020001c  L3                              
20200020  R1                              
20200024  R3                              
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[155 symbols]
