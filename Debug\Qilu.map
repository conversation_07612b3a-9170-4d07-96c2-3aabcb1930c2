******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 18:19:27 2025

OUTPUT FILE NAME:   <Qilu.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000ba9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000ce0  0001f320  R  X
  SRAM                  20200000   00008000  000002d2  00007d2e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000ce0   00000ce0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000be0   00000be0    r-x .text
  00000ca0    00000ca0    00000010   00000010    r-- .rodata
  00000cb0    00000cb0    00000030   00000030    r-- .cinit
20200000    20200000    000000d2   00000000    rw-
  20200000    20200000    000000d0   00000000    rw- .bss
  202000d0    202000d0    00000002   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000be0     
                  000000c0    00000724     empty.o (.text.main)
                  000007e4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000008e8    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000982    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000984    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00000a14    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000a90    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000af8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000b38    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000b74    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000ba8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000bd0    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000bf0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000c0c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000c28    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000c40    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000c56    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00000c68    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000c78    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  00000c82    00000002     --HOLE-- [fill = 0]
                  00000c84    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000c8c    00000006     libc.a : exit.c.obj (.text:abort)
                  00000c92    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000c96    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000c9a    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000c9e    00000002     --HOLE-- [fill = 0]

.cinit     0    00000cb0    00000030     
                  00000cb0    0000000c     (__TI_handler_table)
                  00000cbc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000cc4    00000006     (.cinit..data.load) [load image, compression = lzss]
                  00000cca    00000002     --HOLE-- [fill = 0]
                  00000ccc    00000010     (__TI_cinit_table)
                  00000cdc    00000004     --HOLE-- [fill = 0]

.rodata    0    00000ca0    00000010     
                  00000ca0    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00000ca8    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00000cab    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000d0     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_0Backup)
                  202000bc    00000004     (.common:L1)
                  202000c0    00000004     (.common:L3)
                  202000c4    00000004     (.common:M)
                  202000c8    00000004     (.common:R1)
                  202000cc    00000004     (.common:R3)

.data      0    202000d0    00000002     UNINITIALIZED
                  202000d0    00000001     empty.o (.data.STOP_COUNT)
                  202000d1    00000001     empty.o (.data.count)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       empty.o                        1828   0         22     
       ti_msp_dl_config.o             396    11        188    
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         2230   203       210    
                                                              
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      42        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   3036   245       722    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000ccc records: 2, size/record: 8, table size: 16
	.bss: load addr=00000cbc, load size=00000008 bytes, run addr=20200000, run size=000000d0 bytes, compression=zero_init
	.data: load addr=00000cc4, load size=00000006 bytes, run addr=202000d0, run size=00000002 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000cb0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000983  ADC0_IRQHandler                 
00000983  ADC1_IRQHandler                 
00000983  AES_IRQHandler                  
00000c92  C$$EXIT                         
00000983  CANFD0_IRQHandler               
00000983  DAC0_IRQHandler                 
00000c79  DL_Common_delayCycles           
000007e5  DL_Timer_initFourCCPWMMode      
00000bf1  DL_Timer_setCaptCompUpdateMethod
00000c29  DL_Timer_setCaptureCompareOutCtl
00000c69  DL_Timer_setCaptureCompareValue 
00000c0d  DL_Timer_setClockConfig         
00000983  DMA_IRQHandler                  
00000983  Default_Handler                 
00000983  GROUP0_IRQHandler               
00000983  GROUP1_IRQHandler               
00000c93  HOSTexit                        
00000983  HardFault_Handler               
00000983  I2C0_IRQHandler                 
00000983  I2C1_IRQHandler                 
202000bc  L1                              
202000c0  L3                              
202000c4  M                               
00000983  NMI_Handler                     
00000983  PendSV_Handler                  
202000c8  R1                              
202000cc  R3                              
00000983  RTC_IRQHandler                  
00000c97  Reset_Handler                   
00000983  SPI0_IRQHandler                 
00000983  SPI1_IRQHandler                 
202000d0  STOP_COUNT                      
00000983  SVC_Handler                     
00000a91  SYSCFG_DL_GPIO_init             
00000985  SYSCFG_DL_PWM_0_init            
00000af9  SYSCFG_DL_SYSCTL_init           
00000bd1  SYSCFG_DL_init                  
00000b75  SYSCFG_DL_initPower             
00000983  SysTick_Handler                 
00000983  TIMA0_IRQHandler                
00000983  TIMA1_IRQHandler                
00000983  TIMG0_IRQHandler                
00000983  TIMG12_IRQHandler               
00000983  TIMG6_IRQHandler                
00000983  TIMG7_IRQHandler                
00000983  TIMG8_IRQHandler                
00000983  UART0_IRQHandler                
00000983  UART1_IRQHandler                
00000983  UART2_IRQHandler                
00000983  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000ccc  __TI_CINIT_Base                 
00000cdc  __TI_CINIT_Limit                
00000cdc  __TI_CINIT_Warm                 
00000cb0  __TI_Handler_Table_Base         
00000cbc  __TI_Handler_Table_Limit        
00000b39  __TI_auto_init_nobinit_nopinit  
00000a15  __TI_decompress_lzss            
00000c57  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00000c41  __TI_zero_init_nomemset         
00000c85  __aeabi_memcpy                  
00000c85  __aeabi_memcpy4                 
00000c85  __aeabi_memcpy8                 
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000ba9  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00000c9b  _system_pre_init                
00000c8d  abort                           
ffffffff  binit                           
202000d1  count                           
20200000  gPWM_0Backup                    
00000000  interruptVectors                
000000c1  main                            
000008e9  memcpy                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  main                            
00000200  __STACK_SIZE                    
000007e5  DL_Timer_initFourCCPWMMode      
000008e9  memcpy                          
00000983  ADC0_IRQHandler                 
00000983  ADC1_IRQHandler                 
00000983  AES_IRQHandler                  
00000983  CANFD0_IRQHandler               
00000983  DAC0_IRQHandler                 
00000983  DMA_IRQHandler                  
00000983  Default_Handler                 
00000983  GROUP0_IRQHandler               
00000983  GROUP1_IRQHandler               
00000983  HardFault_Handler               
00000983  I2C0_IRQHandler                 
00000983  I2C1_IRQHandler                 
00000983  NMI_Handler                     
00000983  PendSV_Handler                  
00000983  RTC_IRQHandler                  
00000983  SPI0_IRQHandler                 
00000983  SPI1_IRQHandler                 
00000983  SVC_Handler                     
00000983  SysTick_Handler                 
00000983  TIMA0_IRQHandler                
00000983  TIMA1_IRQHandler                
00000983  TIMG0_IRQHandler                
00000983  TIMG12_IRQHandler               
00000983  TIMG6_IRQHandler                
00000983  TIMG7_IRQHandler                
00000983  TIMG8_IRQHandler                
00000983  UART0_IRQHandler                
00000983  UART1_IRQHandler                
00000983  UART2_IRQHandler                
00000983  UART3_IRQHandler                
00000985  SYSCFG_DL_PWM_0_init            
00000a15  __TI_decompress_lzss            
00000a91  SYSCFG_DL_GPIO_init             
00000af9  SYSCFG_DL_SYSCTL_init           
00000b39  __TI_auto_init_nobinit_nopinit  
00000b75  SYSCFG_DL_initPower             
00000ba9  _c_int00_noargs                 
00000bd1  SYSCFG_DL_init                  
00000bf1  DL_Timer_setCaptCompUpdateMethod
00000c0d  DL_Timer_setClockConfig         
00000c29  DL_Timer_setCaptureCompareOutCtl
00000c41  __TI_zero_init_nomemset         
00000c57  __TI_decompress_none            
00000c69  DL_Timer_setCaptureCompareValue 
00000c79  DL_Common_delayCycles           
00000c85  __aeabi_memcpy                  
00000c85  __aeabi_memcpy4                 
00000c85  __aeabi_memcpy8                 
00000c8d  abort                           
00000c92  C$$EXIT                         
00000c93  HOSTexit                        
00000c97  Reset_Handler                   
00000c9b  _system_pre_init                
00000cb0  __TI_Handler_Table_Base         
00000cbc  __TI_Handler_Table_Limit        
00000ccc  __TI_CINIT_Base                 
00000cdc  __TI_CINIT_Limit                
00000cdc  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_0Backup                    
202000bc  L1                              
202000c0  L3                              
202000c4  M                               
202000c8  R1                              
202000cc  R3                              
202000d0  STOP_COUNT                      
202000d1  count                           
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[94 symbols]
