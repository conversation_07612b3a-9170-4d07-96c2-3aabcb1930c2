<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o Qilu.out -mQilu.map -iC:/TI/mspm0_sdk_2_05_01_00/source -iD:/TI/ZeGeNB -iD:/TI/ZeGeNB/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=Qilu_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x688c94af</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\TI\ZeGeNB\Debug\Qilu.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xba9</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\TI\ZeGeNB\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\TI\ZeGeNB\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\TI\ZeGeNB\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>D:\TI\ZeGeNB\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-29">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d3">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x724</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x7e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e4</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-78">
         <name>.text:memcpy</name>
         <load_address>0x8e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8e8</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x982</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x982</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.SYSCFG_DL_PWM_0_init</name>
         <load_address>0x984</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x984</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xa14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa14</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0xa90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa90</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xaf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xb38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb38</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xb74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb74</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xba8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xbd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbd0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0xbf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbf0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xc0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0xc28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc28</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0xc40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc40</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xc56</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc56</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0xc68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc68</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xc78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc78</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xc84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc84</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text:abort</name>
         <load_address>0xc8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc8c</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.HOSTexit</name>
         <load_address>0xc92</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc92</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xc96</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc96</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0xc9a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc9a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-123">
         <name>__TI_handler_table</name>
         <load_address>0xcb0</load_address>
         <readonly>true</readonly>
         <run_address>0xcb0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-126">
         <name>.cinit..bss.load</name>
         <load_address>0xcbc</load_address>
         <readonly>true</readonly>
         <run_address>0xcbc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-125">
         <name>.cinit..data.load</name>
         <load_address>0xcc4</load_address>
         <readonly>true</readonly>
         <run_address>0xcc4</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-124">
         <name>__TI_cinit_table</name>
         <load_address>0xccc</load_address>
         <readonly>true</readonly>
         <run_address>0xccc</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d8">
         <name>.rodata.gPWM_0Config</name>
         <load_address>0xca0</load_address>
         <readonly>true</readonly>
         <run_address>0xca0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.rodata.gPWM_0ClockConfig</name>
         <load_address>0xca8</load_address>
         <readonly>true</readonly>
         <run_address>0xca8</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-87">
         <name>.data.count</name>
         <load_address>0x202000d1</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000d1</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-86">
         <name>.data.STOP_COUNT</name>
         <load_address>0x202000d0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000d0</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-88">
         <name>.common:L3</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-8f">
         <name>.common:L1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-90">
         <name>.common:M</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-91">
         <name>.common:R1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-92">
         <name>.common:R3</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000cc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-ba">
         <name>.common:gPWM_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-128">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1a2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_loc</name>
         <load_address>0x1a2</load_address>
         <run_address>0x1a2</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_loc</name>
         <load_address>0x25a</load_address>
         <run_address>0x25a</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_loc</name>
         <load_address>0x26d</load_address>
         <run_address>0x26d</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x1c94</load_address>
         <run_address>0x1c94</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_loc</name>
         <load_address>0x1d6c</load_address>
         <run_address>0x1d6c</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_loc</name>
         <load_address>0x2190</load_address>
         <run_address>0x2190</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_loc</name>
         <load_address>0x22fc</load_address>
         <run_address>0x22fc</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_loc</name>
         <load_address>0x236b</load_address>
         <run_address>0x236b</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_loc</name>
         <load_address>0x24d2</load_address>
         <run_address>0x24d2</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_abbrev</name>
         <load_address>0x1c8</load_address>
         <run_address>0x1c8</run_address>
         <size>0x1eb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x3b3</load_address>
         <run_address>0x3b3</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_abbrev</name>
         <load_address>0x420</load_address>
         <run_address>0x420</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_abbrev</name>
         <load_address>0x482</load_address>
         <run_address>0x482</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x708</load_address>
         <run_address>0x708</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0x7b7</load_address>
         <run_address>0x7b7</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_abbrev</name>
         <load_address>0x927</load_address>
         <run_address>0x927</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x960</load_address>
         <run_address>0x960</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_abbrev</name>
         <load_address>0xa22</load_address>
         <run_address>0xa22</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0xa92</load_address>
         <run_address>0xa92</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0xb1f</load_address>
         <run_address>0xb1f</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_abbrev</name>
         <load_address>0xbb7</load_address>
         <run_address>0xbb7</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_abbrev</name>
         <load_address>0xbe3</load_address>
         <run_address>0xbe3</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_abbrev</name>
         <load_address>0xc0a</load_address>
         <run_address>0xc0a</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_abbrev</name>
         <load_address>0xc2f</load_address>
         <run_address>0xc2f</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x223a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_info</name>
         <load_address>0x223a</load_address>
         <run_address>0x223a</run_address>
         <size>0x2238</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4472</load_address>
         <run_address>0x4472</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_info</name>
         <load_address>0x44f2</load_address>
         <run_address>0x44f2</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_info</name>
         <load_address>0x4567</load_address>
         <run_address>0x4567</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x76d9</load_address>
         <run_address>0x76d9</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_info</name>
         <load_address>0x7afc</load_address>
         <run_address>0x7afc</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x8240</load_address>
         <run_address>0x8240</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0x8286</load_address>
         <run_address>0x8286</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x8418</load_address>
         <run_address>0x8418</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x84de</load_address>
         <run_address>0x84de</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_info</name>
         <load_address>0x865a</load_address>
         <run_address>0x865a</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_info</name>
         <load_address>0x8752</load_address>
         <run_address>0x8752</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_info</name>
         <load_address>0x878d</load_address>
         <run_address>0x878d</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_info</name>
         <load_address>0x8926</load_address>
         <run_address>0x8926</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_info</name>
         <load_address>0x8c20</load_address>
         <run_address>0x8c20</run_address>
         <size>0x86</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_ranges</name>
         <load_address>0x58</load_address>
         <run_address>0x58</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x98</load_address>
         <run_address>0x98</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_ranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x288</load_address>
         <run_address>0x288</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_ranges</name>
         <load_address>0x2d0</load_address>
         <run_address>0x2d0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_ranges</name>
         <load_address>0x318</load_address>
         <run_address>0x318</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x330</load_address>
         <run_address>0x330</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_ranges</name>
         <load_address>0x380</load_address>
         <run_address>0x380</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_ranges</name>
         <load_address>0x398</load_address>
         <run_address>0x398</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x797</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_str</name>
         <load_address>0x797</load_address>
         <run_address>0x797</run_address>
         <size>0x1b9b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x2332</load_address>
         <run_address>0x2332</run_address>
         <size>0x13d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_str</name>
         <load_address>0x246f</load_address>
         <run_address>0x246f</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_str</name>
         <load_address>0x25dc</load_address>
         <run_address>0x25dc</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x43a8</load_address>
         <run_address>0x43a8</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_str</name>
         <load_address>0x45cd</load_address>
         <run_address>0x45cd</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_str</name>
         <load_address>0x48fc</load_address>
         <run_address>0x48fc</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0x49f1</load_address>
         <run_address>0x49f1</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_str</name>
         <load_address>0x4b8c</load_address>
         <run_address>0x4b8c</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x4cf4</load_address>
         <run_address>0x4cf4</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_str</name>
         <load_address>0x4ec9</load_address>
         <run_address>0x4ec9</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_str</name>
         <load_address>0x5011</load_address>
         <run_address>0x5011</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_frame</name>
         <load_address>0xd4</load_address>
         <run_address>0xd4</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x190</load_address>
         <run_address>0x190</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_frame</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x5e8</load_address>
         <run_address>0x5e8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0x678</load_address>
         <run_address>0x678</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_frame</name>
         <load_address>0x778</load_address>
         <run_address>0x778</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_frame</name>
         <load_address>0x798</load_address>
         <run_address>0x798</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x7d0</load_address>
         <run_address>0x7d0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x7f8</load_address>
         <run_address>0x7f8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_frame</name>
         <load_address>0x828</load_address>
         <run_address>0x828</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_frame</name>
         <load_address>0x858</load_address>
         <run_address>0x858</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x658</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_line</name>
         <load_address>0x658</load_address>
         <run_address>0x658</run_address>
         <size>0x4af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0xb07</load_address>
         <run_address>0xb07</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_line</name>
         <load_address>0xbbf</load_address>
         <run_address>0xbbf</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_line</name>
         <load_address>0xd37</load_address>
         <run_address>0xd37</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x24a5</load_address>
         <run_address>0x24a5</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x2681</load_address>
         <run_address>0x2681</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_line</name>
         <load_address>0x2b9b</load_address>
         <run_address>0x2b9b</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_line</name>
         <load_address>0x2bd9</load_address>
         <run_address>0x2bd9</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x2cd7</load_address>
         <run_address>0x2cd7</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x2d97</load_address>
         <run_address>0x2d97</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_line</name>
         <load_address>0x2f5f</load_address>
         <run_address>0x2f5f</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_line</name>
         <load_address>0x2fc6</load_address>
         <run_address>0x2fc6</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x3007</load_address>
         <run_address>0x3007</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_line</name>
         <load_address>0x30ab</load_address>
         <run_address>0x30ab</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xbe0</size>
         <contents>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xcb0</load_address>
         <run_address>0xcb0</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-124"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xca0</load_address>
         <run_address>0xca0</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-d7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-ed"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202000d0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-86"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0xd0</size>
         <contents>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-ba"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-128"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e4" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e5" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e6" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e7" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e8" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e9" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-eb" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-107" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24f8</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-9b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-109" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc3e</size>
         <contents>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-12a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10b" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8ca6</size>
         <contents>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-129"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10d" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3c0</size>
         <contents>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-76"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10f" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x50fa</size>
         <contents>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-da"/>
         </contents>
      </logical_group>
      <logical_group id="lg-111" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x878</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-c3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-113" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x314b</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-77"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11d" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-75"/>
         </contents>
      </logical_group>
      <logical_group id="lg-127" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-130" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xce0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-131" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0xd2</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-132" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xce0</used_space>
         <unused_space>0x1f320</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xbe0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xca0</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xcb0</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xce0</start_address>
               <size>0x1f320</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x2d2</used_space>
         <unused_space>0x7d2e</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-e9"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-eb"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xd0</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202000d0</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202000d2</start_address>
               <size>0x7d2e</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0xcbc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xd0</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0xcc4</load_address>
            <load_size>0x6</load_size>
            <run_address>0x202000d0</run_address>
            <run_size>0x2</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xccc</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xcdc</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xcdc</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xcb0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xcbc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-41">
         <name>main</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-42">
         <name>STOP_COUNT</name>
         <value>0x202000d0</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-43">
         <name>count</name>
         <value>0x202000d1</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-44">
         <name>L3</name>
         <value>0x202000c0</value>
      </symbol>
      <symbol id="sm-45">
         <name>L1</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-46">
         <name>M</name>
         <value>0x202000c4</value>
      </symbol>
      <symbol id="sm-47">
         <name>R1</name>
         <value>0x202000c8</value>
      </symbol>
      <symbol id="sm-48">
         <name>R3</name>
         <value>0x202000cc</value>
      </symbol>
      <symbol id="sm-63">
         <name>SYSCFG_DL_init</name>
         <value>0xbd1</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-64">
         <name>SYSCFG_DL_initPower</name>
         <value>0xb75</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-65">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0xa91</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-66">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xaf9</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-67">
         <name>SYSCFG_DL_PWM_0_init</name>
         <value>0x985</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-68">
         <name>gPWM_0Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-73">
         <name>Default_Handler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>Reset_Handler</name>
         <value>0xc97</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-75">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-76">
         <name>NMI_Handler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>HardFault_Handler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>SVC_Handler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>PendSV_Handler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SysTick_Handler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>GROUP0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>GROUP1_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>TIMG8_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>UART3_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>ADC0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>ADC1_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>CANFD0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>DAC0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>SPI0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>SPI1_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>UART1_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>UART2_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>UART0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>TIMG0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>TIMG6_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>TIMA0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>TIMA1_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>TIMG7_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>TIMG12_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>I2C0_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>I2C1_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>AES_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>RTC_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>DMA_IRQHandler</name>
         <value>0x983</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-94">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-95">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-96">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-97">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-98">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-99">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9a">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9b">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a4">
         <name>DL_Common_delayCycles</name>
         <value>0xc79</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-bb">
         <name>DL_Timer_setClockConfig</name>
         <value>0xc0d</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-bc">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0xc69</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-bd">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0xbf1</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-be">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0xc29</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-bf">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x7e5</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-ca">
         <name>_c_int00_noargs</name>
         <value>0xba9</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-cb">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-d7">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xb39</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-df">
         <name>_system_pre_init</name>
         <value>0xc9b</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-ea">
         <name>__TI_zero_init_nomemset</name>
         <value>0xc41</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-f3">
         <name>__TI_decompress_none</name>
         <value>0xc57</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-fe">
         <name>__TI_decompress_lzss</name>
         <value>0xa15</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-108">
         <name>abort</name>
         <value>0xc8d</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-112">
         <name>HOSTexit</name>
         <value>0xc93</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-113">
         <name>C$$EXIT</name>
         <value>0xc92</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-119">
         <name>__aeabi_memcpy</name>
         <value>0xc85</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-11a">
         <name>__aeabi_memcpy4</name>
         <value>0xc85</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-11b">
         <name>__aeabi_memcpy8</name>
         <value>0xc85</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-135">
         <name>memcpy</name>
         <value>0x8e9</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-136">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-139">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-13a">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
